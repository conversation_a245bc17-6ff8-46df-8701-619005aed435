package com.ms.bp.application.data;

import com.ms.bp.application.ImportJobStatusService;
import com.ms.bp.domain.file.areaoutlookplan.OutlookPlanImportService;
import com.ms.bp.domain.file.model.ImportJobStatus;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.db.AuroraConnectionManager;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.util.FunctionUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.sql.Connection;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class OutlookPlanImportServiceTest {

    private String fileType;

    private String rirekiNo;

    private OutlookPlanImportService outlookPlanImportService;

    @BeforeEach
    void init(){
        // 前処理 パラメータ設定、ファイル移動など

    }

    @AfterEach
    void afterExe(){
        // 登録したデータ削除、ファイル元位置に移動など
        //delUploadRrk();
    }


    @Test
    void testArea(){
        // ファイル種類設定
        fileType = BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE;
        outlookPlanImportService = new OutlookPlanImportService(fileType);

        // 履歴番号取得
        rirekiNo = getRirekiNo();

        // 各テストケースを呼び出し
        testFileCheckErr_headItem();
        testFileCheckErr_1件();
        testFileCheckErr_99件();
        testFileCheckErr_100件();
        testFileCheckErr_101件();
        testKengenCheckErr_1();
        testKengenCheckErr_2();
        testSuccess_Ins();
        testSuccess_Upd();

        assertNotNull(fileType);
    }

    @Test
    void testHeadOffice(){
        // ファイル種類設定
        fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        outlookPlanImportService = new OutlookPlanImportService(fileType);

        // 履歴番号取得
        rirekiNo = getRirekiNo();
        // 各テストケースを呼び出し
        testFileCheckErr_headItem();
        testFileCheckErr_1件();
        testFileCheckErr_99件();
        testFileCheckErr_100件();
        testFileCheckErr_101件();
        testKengenCheckErr_1();
        testKengenCheckErr_2();
        testSuccess_Ins();
        testSuccess_Upd();

    }

    // --------------各テスト処理-------------------------


    @Test
    void testFileCheckErr_headItem(){
        // csvデータ設定

    }

    @Test
    void testFileCheckErr_1件(){

    }

    @Test
    void testFileCheckErr_99件(){

    }

    @Test
    void testFileCheckErr_100件(){

    }

    @Test
    void testFileCheckErr_101件(){

    }

    @Test
    void testKengenCheckErr_1(){
        //  パラメータ.エリアコードが、レスポンス.エリアリスト.エリアコードに含まれない
    }

    @Test
    void testKengenCheckErr_2(){
        //  レスポンス.役職区分判定要否 = 1:要　の場合　AND
        //  レスポンス.役職区分 = 51（ＵＬ、ＤＣ長） または 61（非役職者）の場合
    }

    @Test
    void testKengenCheckErr_3(){
        // 下記条件以外
        //  レスポンス.役職区分判定要否 = 1:要　の場合　AND
        //  レスポンス.役職区分 = 51（ＵＬ、ＤＣ長） または 61（非役職者）の場合
    }

    @Test
    void testSuccess_Ins(){
        // 正常系、データなし（登録）
    }

    @Test
    void testSuccess_Upd(){
        // 正常系、データあり（更新）
    }


    private String getRirekiNo()
    {
        String rireki = "";
        ImportJobStatusService importJobStatusService = new ImportJobStatusService();

        String functionId = "BAT_006";
        UserInfo userInfo = createTestUserInfo();

        // ジョブステータスを初期化（データベースに保存）
        ImportJobStatus jobStatus = ImportJobStatus.builder()
                .systmUnyoKigyoCode(userInfo.getSystemOperationCompanyCode())
                .shainCode(userInfo.getShainCode())
                .fileShbts(fileType)
                .area(userInfo.getAreaCode())
                .build().init(functionId);

        // データベースに保存し、自動生成された履歴番号を取得
        rireki = importJobStatusService.createJob(jobStatus).toString();

        return rireki;
    }


    /**
     * テスト用UserInfoオブジェクトを作成
     */
    private UserInfo createTestUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("093540");
        userInfo.setSystemOperationCompanyCode("100001");
        userInfo.setUnitCode("99999");
        userInfo.setAreaCode("0001");
        userInfo.setAreaName("統合テストエリア");
        userInfo.setPositionCode("99");
        return userInfo;
    }

    private String delUploadRrk()
    {
        String sql = " delete from T_UPLOAD_RRK where rrk_bango ='" + rirekiNo + "'";

        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            jdbcTemplate.update(sql, null);

        } catch (SQLException e) {
            System.err.println("PostgreSQL アップロード履歴削除エラー: " + e.getMessage());
            e.printStackTrace();

            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }

        return sql;
    }
}