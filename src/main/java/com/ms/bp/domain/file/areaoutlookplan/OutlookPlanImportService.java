package com.ms.bp.domain.file.areaoutlookplan;

import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.domain.file.model.OutlookPlanInfo;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.master.repository.GroupAreaRepository;
import com.ms.bp.domain.master.repository.GroupMasterRepository;
import com.ms.bp.domain.master.repository.UnitMasterRepository;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.repository.impl.GroupAreaRepositoryImpl;
import com.ms.bp.infrastructure.repository.impl.GroupMasterRepositoryImpl;
import com.ms.bp.infrastructure.repository.impl.UnitMasterRepositoryImpl;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ValidationError;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validator.DTODataValidator;
import com.ms.bp.shared.common.io.validator.DataValidator;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.ExportFileNameUtil;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.shared.util.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Supplier;

import static com.ms.bp.shared.util.MessageCodeUtil.formatMessage;

public class OutlookPlanImportService extends AbstractImportService<OutlookPlanInfo> {

    private static final Logger logger = LoggerFactory.getLogger(OutlookPlanImportService.class);

    private final String inputFileType;

    public OutlookPlanImportService(String fileType)
    {
        logger.debug("OutlookPlanImportServiceが初期化されました");
        inputFileType = fileType;
    }
    @Override
    protected Class<OutlookPlanInfo> getDTOClass() {
        return OutlookPlanInfo.class;
    }

    @Override
    protected ImportOptions buildImportOptions()
    {
        // CSVヘッダーフィールドマッピングを構築
        Map<String, String> headerMapping = new HashMap<>();
        headerMapping.put("ｴﾘｱｺｰﾄﾞ", "areaCode");
        headerMapping.put("ﾕﾆｯﾄCD", "unitCode");
        headerMapping.put("担当名", "tantoshaName");
        headerMapping.put("企業CD", "kigyoCode");
        headerMapping.put("ｶﾃｺﾞﾘ", "kategori");
        headerMapping.put("ｻﾌﾞｶﾃ", "subKategori");
        headerMapping.put("業態(事業計画)", "gyotai");
        headerMapping.put("変更後取組区分", "afterTorikumKbn");
        headerMapping.put("業態比率", "gyotaiHiritsu");

        // 各種金額
        headerMapping.put("計画4月総売上高在庫", "planAprAllSalInven");
        headerMapping.put("計画4月総売上高直送", "planAprAllSalChokusou");
        headerMapping.put("計画4月返品在庫", "planAprReturnInven");
        headerMapping.put("計画4月返品直送", "planAprReturnChokusou");
        headerMapping.put("計画4月リベート在庫", "planAprRebateInven");
        headerMapping.put("計画4月リベート直送", "planAprRebateChokusou");
        headerMapping.put("計画4月センターフィ在庫", "planAprCenterFeeInven");
        headerMapping.put("計画4月センターフィ直送", "planAprCenterFeeChokusou");
        headerMapping.put("計画4月直接利益在庫", "planAprChokuRiekiInven");
        headerMapping.put("計画4月直接利益直送", "planAprChokuRiekiChokusou");
        headerMapping.put("計画5月総売上高在庫", "planMayAllSalInven");
        headerMapping.put("計画5月総売上高直送", "planMayAllSalChokusou");
        headerMapping.put("計画5月返品在庫", "planMayReturnInven");
        headerMapping.put("計画5月返品直送", "planMayReturnChokusou");
        headerMapping.put("計画5月リベート在庫", "planMayRebateInven");
        headerMapping.put("計画5月リベート直送", "planMayRebateChokusou");
        headerMapping.put("計画5月センターフィ在庫", "planMayCenterFeeInven");
        headerMapping.put("計画5月センターフィ直送", "planMayCenterFeeChokusou");
        headerMapping.put("計画5月直接利益在庫", "planMayChokuRiekiInven");
        headerMapping.put("計画5月直接利益直送", "planMayChokuRiekiChokusou");
        headerMapping.put("計画6月総売上高在庫", "planJunAllSalInven");
        headerMapping.put("計画6月総売上高直送", "planJunAllSalChokusou");
        headerMapping.put("計画6月返品在庫", "planJunReturnInven");
        headerMapping.put("計画6月返品直送", "planJunReturnChokusou");
        headerMapping.put("計画6月リベート在庫", "planJunRebateInven");
        headerMapping.put("計画6月リベート直送", "planJunRebateChokusou");
        headerMapping.put("計画6月センターフィ在庫", "planJunCenterFeeInven");
        headerMapping.put("計画6月センターフィ直送", "planJunCenterFeeChokusou");
        headerMapping.put("計画6月直接利益在庫", "planJunChokuRiekiInven");
        headerMapping.put("計画6月直接利益直送", "planJunChokuRiekiChokusou");
        headerMapping.put("計画7月総売上高在庫", "planJulAllSalInven");
        headerMapping.put("計画7月総売上高直送", "planJulAllSalChokusou");
        headerMapping.put("計画7月返品在庫", "planJulReturnInven");
        headerMapping.put("計画7月返品直送", "planJulReturnChokusou");
        headerMapping.put("計画7月リベート在庫", "planJulRebateInven");
        headerMapping.put("計画7月リベート直送", "planJulRebateChokusou");
        headerMapping.put("計画7月センターフィ在庫", "planJulCenterFeeInven");
        headerMapping.put("計画7月センターフィ直送", "planJulCenterFeeChokusou");
        headerMapping.put("計画7月直接利益在庫", "planJulChokuRiekiInven");
        headerMapping.put("計画7月直接利益直送", "planJulChokuRiekiChokusou");
        headerMapping.put("計画8月総売上高在庫", "planAugAllSalInven");
        headerMapping.put("計画8月総売上高直送", "planAugAllSalChokusou");
        headerMapping.put("計画8月返品在庫", "planAugReturnInven");
        headerMapping.put("計画8月返品直送", "planAugReturnChokusou");
        headerMapping.put("計画8月リベート在庫", "planAugRebateInven");
        headerMapping.put("計画8月リベート直送", "planAugRebateChokusou");
        headerMapping.put("計画8月センターフィ在庫", "planAugCenterFeeInven");
        headerMapping.put("計画8月センターフィ直送", "planAugCenterFeeChokusou");
        headerMapping.put("計画8月直接利益在庫", "planAugChokuRiekiInven");
        headerMapping.put("計画8月直接利益直送", "planAugChokuRiekiChokusou");
        headerMapping.put("計画9月総売上高在庫", "planSepAllSalInven");
        headerMapping.put("計画9月総売上高直送", "planSepAllSalChokusou");
        headerMapping.put("計画9月返品在庫", "planSepReturnInven");
        headerMapping.put("計画9月返品直送", "planSepReturnChokusou");
        headerMapping.put("計画9月リベート在庫", "planSepRebateInven");
        headerMapping.put("計画9月リベート直送", "planSepRebateChokusou");
        headerMapping.put("計画9月センターフィ在庫", "planSepCenterFeeInven");
        headerMapping.put("計画9月センターフィ直送", "planSepCenterFeeChokusou");
        headerMapping.put("計画9月直接利益在庫", "planSepChokuRiekiInven");
        headerMapping.put("計画9月直接利益直送", "planSepChokuRiekiChokusou");
        headerMapping.put("計画10月総売上高在庫", "planOctAllSalInven");
        headerMapping.put("計画10月総売上高直送", "planOctAllSalChokusou");
        headerMapping.put("計画10月返品在庫", "planOctReturnInven");
        headerMapping.put("計画10月返品直送", "planOctReturnChokusou");
        headerMapping.put("計画10月リベート在庫", "planOctRebateInven");
        headerMapping.put("計画10月リベート直送", "planOctRebateChokusou");
        headerMapping.put("計画10月センターフィ在庫", "planOctCenterFeeInven");
        headerMapping.put("計画10月センターフィ直送", "planOctCenterFeeChokusou");
        headerMapping.put("計画10月直接利益在庫", "planOctChokuRiekiInven");
        headerMapping.put("計画10月直接利益直送", "planOctChokuRiekiChokusou");
        headerMapping.put("計画11月総売上高在庫", "planNovAllSalInven");
        headerMapping.put("計画11月総売上高直送", "planNovAllSalChokusou");
        headerMapping.put("計画11月返品在庫", "planNovReturnInven");
        headerMapping.put("計画11月返品直送", "planNovReturnChokusou");
        headerMapping.put("計画11月リベート在庫", "planNovRebateInven");
        headerMapping.put("計画11月リベート直送", "planNovRebateChokusou");
        headerMapping.put("計画11月センターフィ在庫", "planNovCenterFeeInven");
        headerMapping.put("計画11月センターフィ直送", "planNovCenterFeeChokusou");
        headerMapping.put("計画11月直接利益在庫", "planNovChokuRiekiInven");
        headerMapping.put("計画11月直接利益直送", "planNovChokuRiekiChokusou");
        headerMapping.put("計画12月総売上高在庫", "planDecAllSalInven");
        headerMapping.put("計画12月総売上高直送", "planDecAllSalChokusou");
        headerMapping.put("計画12月返品在庫", "planDecReturnInven");
        headerMapping.put("計画12月返品直送", "planDecReturnChokusou");
        headerMapping.put("計画12月リベート在庫", "planDecRebateInven");
        headerMapping.put("計画12月リベート直送", "planDecRebateChokusou");
        headerMapping.put("計画12月センターフィ在庫", "planDecCenterFeeInven");
        headerMapping.put("計画12月センターフィ直送", "planDecCenterFeeChokusou");
        headerMapping.put("計画12月直接利益在庫", "planDecChokuRiekiInven");
        headerMapping.put("計画12月直接利益直送", "planDecChokuRiekiChokusou");
        headerMapping.put("計画1月総売上高在庫", "planJanAllSalInven");
        headerMapping.put("計画1月総売上高直送", "planJanAllSalChokusou");
        headerMapping.put("計画1月返品在庫", "planJanReturnInven");
        headerMapping.put("計画1月返品直送", "planJanReturnChokusou");
        headerMapping.put("計画1月リベート在庫", "planJanRebateInven");
        headerMapping.put("計画1月リベート直送", "planJanRebateChokusou");
        headerMapping.put("計画1月センターフィ在庫", "planJanCenterFeeInven");
        headerMapping.put("計画1月センターフィ直送", "planJanCenterFeeChokusou");
        headerMapping.put("計画1月直接利益在庫", "planJanChokuRiekiInven");
        headerMapping.put("計画1月直接利益直送", "planJanChokuRiekiChokusou");
        headerMapping.put("計画2月総売上高在庫", "planFebAllSalInven");
        headerMapping.put("計画2月総売上高直送", "planFebAllSalChokusou");
        headerMapping.put("計画2月返品在庫", "planFebReturnInven");
        headerMapping.put("計画2月返品直送", "planFebReturnChokusou");
        headerMapping.put("計画2月リベート在庫", "planFebRebateInven");
        headerMapping.put("計画2月リベート直送", "planFebRebateChokusou");
        headerMapping.put("計画2月センターフィ在庫", "planFebCenterFeeInven");
        headerMapping.put("計画2月センターフィ直送", "planFebCenterFeeChokusou");
        headerMapping.put("計画2月直接利益在庫", "planFebChokuRiekiInven");
        headerMapping.put("計画2月直接利益直送", "planFebChokuRiekiChokusou");
        headerMapping.put("計画3月総売上高在庫", "planMarAllSalInven");
        headerMapping.put("計画3月総売上高直送", "planMarAllSalChokusou");
        headerMapping.put("計画3月返品在庫", "planMarReturnInven");
        headerMapping.put("計画3月返品直送", "planMarReturnChokusou");
        headerMapping.put("計画3月リベート在庫", "planMarRebateInven");
        headerMapping.put("計画3月リベート直送", "planMarRebateChokusou");
        headerMapping.put("計画3月センターフィ在庫", "planMarCenterFeeInven");
        headerMapping.put("計画3月センターフィ直送", "planMarCenterFeeChokusou");
        headerMapping.put("計画3月直接利益在庫", "planMarChokuRiekiInven");
        headerMapping.put("計画3月直接利益直送", "planMarChokuRiekiChokusou");
        headerMapping.put("実績見通し1月総売上高在庫", "outlookJanAllSalInven");
        headerMapping.put("実績見通し1月総売上高直送", "outlookJanAllSalChokusou");
        headerMapping.put("実績見通し1月返品在庫", "outlookJanReturnInven");
        headerMapping.put("実績見通し1月返品直送", "outlookJanReturnChokusou");
        headerMapping.put("実績見通し1月リベート在庫", "outlookJanRebateInven");
        headerMapping.put("実績見通し1月リベート直送", "outlookJanRebateChokusou");
        headerMapping.put("実績見通し1月センターフィ在庫", "outlookJanCenterFeeInven");
        headerMapping.put("実績見通し1月センターフィ直送", "outlookJanCenterFeeChokusou");
        headerMapping.put("実績見通し1月直接利益在庫", "outlookJanChokuRiekiInven");
        headerMapping.put("実績見通し1月直接利益直送", "outlookJanChokuRiekiChokusou");
        headerMapping.put("実績見通し2月総売上高在庫", "outlookFebAllSalInven");
        headerMapping.put("実績見通し2月総売上高直送", "outlookFebAllSalChokusou");
        headerMapping.put("実績見通し2月返品在庫", "outlookFebReturnInven");
        headerMapping.put("実績見通し2月返品直送", "outlookFebReturnChokusou");
        headerMapping.put("実績見通し2月リベート在庫", "outlookFebRebateInven");
        headerMapping.put("実績見通し2月リベート直送", "outlookFebRebateChokusou");
        headerMapping.put("実績見通し2月センターフィ在庫", "outlookFebCenterFeeInven");
        headerMapping.put("実績見通し2月センターフィ直送", "outlookFebCenterFeeChokusou");
        headerMapping.put("実績見通し2月直接利益在庫", "outlookFebChokuRiekiInven");
        headerMapping.put("実績見通し2月直接利益直送", "outlookFebChokuRiekiChokusou");
        headerMapping.put("実績見通し3月総売上高在庫", "outlookMarAllSalInven");
        headerMapping.put("実績見通し3月総売上高直送", "outlookMarAllSalChokusou");
        headerMapping.put("実績見通し3月返品在庫", "outlookMarReturnInven");
        headerMapping.put("実績見通し3月返品直送", "outlookMarReturnChokusou");
        headerMapping.put("実績見通し3月リベート在庫", "outlookMarRebateInven");
        headerMapping.put("実績見通し3月リベート直送", "outlookMarRebateChokusou");
        headerMapping.put("実績見通し3月センターフィ在庫", "outlookMarCenterFeeInven");
        headerMapping.put("実績見通し3月センターフィ直送", "outlookMarCenterFeeChokusou");
        headerMapping.put("実績見通し3月直接利益在庫", "outlookMarChokuRiekiInven");
        headerMapping.put("実績見通し3月直接利益直送", "outlookMarChokuRiekiChokusou");

        // 固定値フィールドを構築（RequestContextからユーザー情報を取得）
        Map<String, Object> additionalFields = buildAdditionalFields();

        String tblName = "T_HNSH_MTSH_KKK_SSNKN_TN_C";
        if(inputFileType.equals(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE))
        {
            tblName = "T_AREA_MTSH_KKK_SSNKN_TN_C";
        }

        return ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .batchSize(BusinessConstants.CSV_UPLOAD_BATCH_SIZE)
                .targetTable(tblName)
                // 複合主キー：年度 + グループコード + 採算管理単位コード
                .keyColumns("NENDO", "GROUP_CODE", "SSNKN_TNCD")
                .upsertMode(true)
                .skipValidation(false)
                .continueOnError(false)
                .errorFileName(ExportFileNameUtil.getFileNameByFileType(inputFileType))
                .enableFieldMapping(true)  // フィールドマッピングを有効化
                .headerFieldMapping(headerMapping)
                .additionalFields(additionalFields)
                .build();
    }

    @Override
    protected String getDataType() {
        // 見通し・計画_採算管理単位C別＜本社＞
        // 見通し・計画_採算管理単位C別＜エリア＞
        return inputFileType;
    }

    @Override
    protected DataValidator getDataValidator() {
        return new DTODataValidator<>(OutlookPlanInfo.class, this::validateCustomLogic);
    }

    /**
     * 固定値フィールドを構築（RequestContextからユーザー情報を取得）
     */
    private Map<String, Object> buildAdditionalFields() {
        Map<String, Object> additionalFields = new HashMap<>();

        // RequestContextからユーザー情報を取得
        UserInfo userInfo = RequestContext.getUserInfo();

        additionalFields.put("NENDO", DateUtil.getNextFiscalYear());
        if (userInfo != null) {
            // ユーザー情報が取得できた場合、実際の値を設定
            String companyCode = userInfo.getSystemOperationCompanyCode();
            String shainCode = userInfo.getShainCode();

            additionalFields.put("SYSTM_UNYO_KIGYO_CODE", companyCode);
            additionalFields.put("TRK_SYSTM_UNYO_KIGYO_CODE", companyCode);
            additionalFields.put("TRK_SHAIN_CODE", shainCode);
            additionalFields.put("KSHN_SYSTM_UNYO_KIGYO_CODE", companyCode);
            additionalFields.put("KSHN_SHAIN_CODE", shainCode);

            logger.debug("ユーザー情報から固定値フィールドを設定: 企業コード={}, 社員コード={}",
                    companyCode, shainCode);
        }

        return additionalFields;
    }

    /**
     * カスタムビジネスロジック検証
     * アノテーション検証では対応できない複雑な検証ロジックを実装
     *
     * 検証内容：
     * 1. 権限チェック（エリアコード、移管先エリアコード）
     * 2. データベース存在性チェック（移管先エリア、グループ、ユニット）
     * 3. 業務ルールに基づく検証
     *
     * @param data 検証対象のデータ
     * @param options インポートオプション
     * @return 検証エラーのリスト
     */
    protected List<ValidationError> validateCustomLogic(Map<String, Object> data, ImportOptions options) {
        List<ValidationError> errors = new ArrayList<>();

        // RequestContextからユーザー情報を取得して権限チェック
        UserInfo userInfo = RequestContext.getUserInfo();
        if (userInfo != null) {
            validatePermissions(data, userInfo, errors);
        }

        // データベース存在性チェック
        validateDatabaseReferences(data, errors);

        return errors;
    }

    /**
     * 権限チェック処理
     */
    private void validatePermissions(Map<String, Object> data, UserInfo userInfo, List<ValidationError> errors) {
        logger.debug("権限チェック開始: ユーザー={}", userInfo.getShainCode());

        // エリアコードの権限チェック
        String areaCode = (String) data.get("areaCode");
        if (areaCode != null && !areaCode.trim().isEmpty()) {
            validateAreaPermission(areaCode, "areaCode", userInfo, errors);
        }

        // 移管先エリアコードの権限チェック
        String ikansakiAreaCode = (String) data.get("ikansakiAreaCode");
        if (ikansakiAreaCode != null && !ikansakiAreaCode.trim().isEmpty()) {
            validateAreaPermission(ikansakiAreaCode, "ikansakiAreaCode", userInfo, errors);
        }

        logger.debug("権限チェック完了: エラー数={}", errors.size());
    }

    /**
     * エリア権限検証
     * ユーザーのエリア権限に基づいてアクセス可能なエリアコードかどうかを検証する
     * 検証ロジック：
     * 1. userInfo.areaInfosが設定されている場合：areaInfos内のareaCodeと照合
     * 2. userInfo.areaInfosが空の場合：userInfo.areaCodeと照合
     * 3. いずれにも該当しない場合は権限エラー
     */
    private void validateAreaPermission(String areaCode, String fieldName, UserInfo userInfo, List<ValidationError> errors) {
        try {
            logger.trace("エリア権限チェック開始: areaCode={}, fieldName={}, user={}",
                    areaCode, fieldName, userInfo.getShainCode());

            boolean hasPermission;
            List<AreaInfo> areaInfos = userInfo.getAreaInfos();

            if (areaInfos != null && !areaInfos.isEmpty()) {
                // areaInfosが設定されている場合：コレクション内のareaCodeと照合
                hasPermission = areaInfos.stream()
                        .anyMatch(areaInfo -> areaCode.equals(areaInfo.getAreaCode()));
            } else {
                // areaInfosが空またはnullの場合：userInfo.areaCodeと照合
                hasPermission = areaCode.equals(userInfo.getAreaCode());
            }

            if (!hasPermission) {
                errors.add(new ValidationError(fieldName, areaCode, formatMessage(GlobalMessageConstants.ERR_008)));
            }

        } catch (Exception e) {
            logger.error("権限チェック中にエラーが発生しました: areaCode={}, user={}",
                    areaCode, userInfo.getShainCode(), e);
            errors.add(new ValidationError(fieldName, areaCode, formatMessage(GlobalMessageConstants.ERR_008)));
        }
    }

    /**
     * データベース存在性チェック
     * LambdaResourceManagerを使用してリソース管理を統一化
     * Repository実例は使用時に作成し、接続リークを防止
     */
    private void validateDatabaseReferences(Map<String, Object> data, List<ValidationError> errors) {
        try {
            // LambdaResourceManagerを使用してデータベース操作を実行
            LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {
                // Repository インスタンスを作成（キャッシュしない）
                GroupAreaRepository groupAreaRepository = new GroupAreaRepositoryImpl(jdbcTemplate);
                GroupMasterRepository groupMasterRepository = new GroupMasterRepositoryImpl(jdbcTemplate);
                UnitMasterRepository unitMasterRepository = new UnitMasterRepositoryImpl(jdbcTemplate);

                // 移管先エリアコードの存在チェック
                String ikansakiAreaCode = (String) data.get("ikansakiAreaCode");
                if (ikansakiAreaCode != null && !ikansakiAreaCode.trim().isEmpty()) {
                    if (!checkMasterExists("組織エリアマスタ", ikansakiAreaCode,
                            () -> groupAreaRepository.existsByAreaCode(ikansakiAreaCode))) {
                        errors.add(new ValidationError("ikansakiAreaCode", ikansakiAreaCode,
                                formatMessage(GlobalMessageConstants.ERR_022,"移管先エリアコード ","CSV.移管先エリアCD")));
                    }
                }

                // 移管先グループコードの存在チェック
                String ikansakiGroupCode = (String) data.get("ikansakiGroupCode");
                if (ikansakiGroupCode != null && !ikansakiGroupCode.trim().isEmpty()) {
                    if (!checkMasterExists("グループマスタ", ikansakiGroupCode,
                            () -> groupMasterRepository.existsByGroupCode(ikansakiGroupCode))) {
                        errors.add(new ValidationError("ikansakiGroupCode", ikansakiGroupCode,
                                formatMessage(GlobalMessageConstants.ERR_022,"移管先グループコード ","CSV.移管先グループCD")));
                    }
                }

                // 移管先ユニットコードの存在チェック
                String ikansakiUnitCode = (String) data.get("ikansakiUnitCode");
                if (ikansakiUnitCode != null && !ikansakiUnitCode.trim().isEmpty()) {
                    if (!checkMasterExists("ユニットマスタ", ikansakiUnitCode,
                            () -> unitMasterRepository.existsByUnitCode(ikansakiUnitCode))) {
                        errors.add(new ValidationError("ikansakiUnitCode", ikansakiUnitCode,
                                formatMessage(GlobalMessageConstants.ERR_022,"移管先ユニットコード ","CSV.移管先ユニットCD")));
                    }
                }

                // 採算管理単位コードの特別処理
                // 要求仕様：採算管理単位マスタにない採算管理単位コードも入力可能
                // そのため、存在チェックは行わない

                return null; // 戻り値は不要
            });

        } catch (Exception e) {
            logger.error("データベース存在性チェック中にエラーが発生しました", e);
            errors.add(new ValidationError("database", "connection",
                    "データベース接続エラー: " + e.getMessage()));
        }
    }

    /**
     * マスタデータの存在チェック（汎用メソッド）
     *
     * @param masterName マスタ名（ログ出力用）
     * @param code 検証対象のコード
     * @param existsCheck 存在チェック処理
     * @return 存在する場合true、存在しない場合またはエラーの場合false
     */
    private boolean checkMasterExists(String masterName, String code, Supplier<Boolean> existsCheck) {
        try {
            return existsCheck.get();
        } catch (Exception e) {
            logger.error("{}存在チェック中にエラーが発生しました: code={}", masterName, code, e);
            // エラーが発生した場合は存在しないものとして扱う
            return false;
        }
    }
}
