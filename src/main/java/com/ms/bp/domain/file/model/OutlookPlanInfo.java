package com.ms.bp.domain.file.model;

import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.io.converter.DatabaseMappable;
import com.ms.bp.shared.common.io.validation.annotation.NumericHalfWidth;
import com.ms.bp.shared.common.io.validation.annotation.Range;
import com.ms.bp.shared.common.io.validation.annotation.Required;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.FunctionUtil;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ファイル処理結果値オブジェクト
 * インポート・エクスポート処理の結果を表現する
 */
@Data
public class OutlookPlanInfo implements DatabaseMappable {

    /**
     * エリアコード
     * 権限チェック対象
     */
    @Required(fieldName = "エリアCD")
    @Range(min = 4, max = 4, fieldName = "エリアCD")
    @NumericHalfWidth(fieldName = "エリアCD")
    private String areaCode;

    /**
     * グループコード
     * 複合主キーの一部
     */
    @Required(fieldName = "グループ")
    @Range(min = 4, max = 4, fieldName = "グループ")
    @NumericHalfWidth(fieldName = "グループ")
    private String groupCode;

    /**
     * ユニットコード
     */
    @Required(fieldName = "ユニット")
    @Range(min = 5, max = 5, fieldName = "ユニット")
    @NumericHalfWidth(fieldName = "ユニット")
    private String unitCode;

    /**
     * 担当者名
     */
    @Range(min = 1, max = 25, fieldName = "担当者")
    private String tantoshaName;

    /**
     * 採算CD7桁
     * 複合主キーの一部
     * マスタにない採算管理単位コードも入力可能
     */
    @Required(fieldName = "採算CD7桁")
    @Range(min = 7, max = 7, fieldName = "採算CD7桁")
    @NumericHalfWidth(fieldName = "採算CD7桁")
    private String saisanCode;

    /**
     * 企業コード
     */
    @Required(fieldName = "企業CD")
    @Range(min = 7, max = 7, fieldName = "企業CD")
    @NumericHalfWidth(fieldName = "企業CD")
    private String kigyoCode;

    /**
     * カテゴリ
     */
    @Range(min = 1, max = 15, fieldName = "カテゴリ")
    private String kategori;

    /**
     * サブカテ
     */
    @Range(min = 1, max = 15, fieldName = "サブカテ")
    private String subKategori;

    /**
     * 業態(事業計画)
     */
    @Range(min = 1, max = 30, fieldName = "業態(事業計画)")
    private String gyotai;

    /**
     * 変更後取組区分
     */
    @Range(min = 1, max = 20, fieldName = "変更後取組区分")
    private String afterTorikumKbn;

    /**
     * 業態比率
     */
    @Range(min = 1, max = 3, fieldName = "業態比率")
    @NumericHalfWidth(fieldName = "業態比率")
    private String gyotaiHiritsu;


    // 計画4月総売上高在庫
    @Required(fieldName = "計画4月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画4月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画4月総売上高在庫")
    private Long planAprAllSalInven;
    // 計画4月総売上高直送
    @Required(fieldName = "計画4月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画4月総売上高直送")
    @NumericHalfWidth(fieldName = "計画4月総売上高直送")
    private Long planAprAllSalChokusou;
    // 計画4月総売上高計
    @Required(fieldName = "計画4月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画4月総売上高計")
    @NumericHalfWidth(fieldName = "計画4月総売上高計")
    private Long planAprAllSalTotal;
    // 計画4月返品在庫
    @Required(fieldName = "計画4月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画4月返品在庫")
    @NumericHalfWidth(fieldName = "計画4月返品在庫")
    private Long planAprReturnInven;
    // 計画4月返品直送
    @Required(fieldName = "計画4月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画4月返品直送")
    @NumericHalfWidth(fieldName = "計画4月返品直送")
    private Long planAprReturnChokusou;
    // 計画4月返品計
    @Required(fieldName = "計画4月返品計")
    @Range(min = 1, max = 10, fieldName = "計画4月返品計")
    @NumericHalfWidth(fieldName = "計画4月返品計")
    private Long planAprReturnTotal;
    // 計画4月リベート在庫
    @Required(fieldName = "計画4月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画4月リベート在庫")
    @NumericHalfWidth(fieldName = "計画4月リベート在庫")
    private Long planAprRebateInven;
    // 計画4月リベート直送
    @Required(fieldName = "計画4月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画4月リベート直送")
    @NumericHalfWidth(fieldName = "計画4月リベート直送")
    private Long planAprRebateChokusou;
    // 計画4月リベート計
    @Required(fieldName = "計画4月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画4月リベート計")
    @NumericHalfWidth(fieldName = "計画4月リベート計")
    private Long planAprRebateTotal;
    // 計画4月センターフィ在庫
    @Required(fieldName = "計画4月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画4月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画4月センターフィ在庫")
    private Long planAprCenterFeeInven;
    // 計画4月センターフィ直送
    @Required(fieldName = "計画4月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画4月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画4月センターフィ直送")
    private Long planAprCenterFeeChokusou;
    // 計画4月センターフィ計
    @Required(fieldName = "計画4月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画4月センターフィ計")
    @NumericHalfWidth(fieldName = "計画4月センターフィ計")
    private Long planAprCenterFeeTotal;
    // 計画4月直接利益在庫
    @Required(fieldName = "計画4月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画4月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画4月直接利益在庫")
    private Long planAprChokuRiekiInven;
    // 計画4月直接利益直送
    @Required(fieldName = "計画4月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画4月直接利益直送")
    @NumericHalfWidth(fieldName = "計画4月直接利益直送")
    private Long planAprChokuRiekiChokusou;
    // 計画4月直接利益計
    @Required(fieldName = "計画4月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画4月直接利益計")
    @NumericHalfWidth(fieldName = "計画4月直接利益計")
    private Long planAprChokuRiekiTotal;
    // 計画4月直利率在庫
    @Required(fieldName = "計画4月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画4月直利率在庫")
    @NumericHalfWidth(fieldName = "計画4月直利率在庫")
    private Long planAprChokuRateInven;
    // 計画4月直利率直送
    @Required(fieldName = "計画4月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画4月直利率直送")
    @NumericHalfWidth(fieldName = "計画4月直利率直送")
    private Long planAprChokuRateChokusou;
    // 計画4月直利率計
    @Required(fieldName = "計画4月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画4月直利率計")
    @NumericHalfWidth(fieldName = "計画4月直利率計")
    private Long planAprChokuRateTotal;
    // 計画5月総売上高在庫
    @Required(fieldName = "計画5月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画5月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画5月総売上高在庫")
    private Long planMayAllSalInven;
    // 計画5月総売上高直送
    @Required(fieldName = "計画5月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画5月総売上高直送")
    @NumericHalfWidth(fieldName = "計画5月総売上高直送")
    private Long planMayAllSalChokusou;
    // 計画5月総売上高計
    @Required(fieldName = "計画5月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画5月総売上高計")
    @NumericHalfWidth(fieldName = "計画5月総売上高計")
    private Long planMayAllSalTotal;
    // 計画5月返品在庫
    @Required(fieldName = "計画5月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画5月返品在庫")
    @NumericHalfWidth(fieldName = "計画5月返品在庫")
    private Long planMayReturnInven;
    // 計画5月返品直送
    @Required(fieldName = "計画5月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画5月返品直送")
    @NumericHalfWidth(fieldName = "計画5月返品直送")
    private Long planMayReturnChokusou;
    // 計画5月返品計
    @Required(fieldName = "計画5月返品計")
    @Range(min = 1, max = 10, fieldName = "計画5月返品計")
    @NumericHalfWidth(fieldName = "計画5月返品計")
    private Long planMayReturnTotal;
    // 計画5月リベート在庫
    @Required(fieldName = "計画5月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画5月リベート在庫")
    @NumericHalfWidth(fieldName = "計画5月リベート在庫")
    private Long planMayRebateInven;
    // 計画5月リベート直送
    @Required(fieldName = "計画5月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画5月リベート直送")
    @NumericHalfWidth(fieldName = "計画5月リベート直送")
    private Long planMayRebateChokusou;
    // 計画5月リベート計
    @Required(fieldName = "計画5月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画5月リベート計")
    @NumericHalfWidth(fieldName = "計画5月リベート計")
    private Long planMayRebateTotal;
    // 計画5月センターフィ在庫
    @Required(fieldName = "計画5月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画5月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画5月センターフィ在庫")
    private Long planMayCenterFeeInven;
    // 計画5月センターフィ直送
    @Required(fieldName = "計画5月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画5月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画5月センターフィ直送")
    private Long planMayCenterFeeChokusou;
    // 計画5月センターフィ計
    @Required(fieldName = "計画5月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画5月センターフィ計")
    @NumericHalfWidth(fieldName = "計画5月センターフィ計")
    private Long planMayCenterFeeTotal;
    // 計画5月直接利益在庫
    @Required(fieldName = "計画5月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画5月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画5月直接利益在庫")
    private Long planMayChokuRiekiInven;
    // 計画5月直接利益直送
    @Required(fieldName = "計画5月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画5月直接利益直送")
    @NumericHalfWidth(fieldName = "計画5月直接利益直送")
    private Long planMayChokuRiekiChokusou;
    // 計画5月直接利益計
    @Required(fieldName = "計画5月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画5月直接利益計")
    @NumericHalfWidth(fieldName = "計画5月直接利益計")
    private Long planMayChokuRiekiTotal;
    // 計画5月直利率在庫
    @Required(fieldName = "計画5月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画5月直利率在庫")
    @NumericHalfWidth(fieldName = "計画5月直利率在庫")
    private Long planMayChokuRateInven;
    // 計画5月直利率直送
    @Required(fieldName = "計画5月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画5月直利率直送")
    @NumericHalfWidth(fieldName = "計画5月直利率直送")
    private Long planMayChokuRateChokusou;
    // 計画5月直利率計
    @Required(fieldName = "計画5月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画5月直利率計")
    @NumericHalfWidth(fieldName = "計画5月直利率計")
    private Long planMayChokuRateTotal;
    // 計画6月総売上高在庫
    @Required(fieldName = "計画6月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画6月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画6月総売上高在庫")
    private Long planJunAllSalInven;
    // 計画6月総売上高直送
    @Required(fieldName = "計画6月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画6月総売上高直送")
    @NumericHalfWidth(fieldName = "計画6月総売上高直送")
    private Long planJunAllSalChokusou;
    // 計画6月総売上高計
    @Required(fieldName = "計画6月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画6月総売上高計")
    @NumericHalfWidth(fieldName = "計画6月総売上高計")
    private Long planJunAllSalTotal;
    // 計画6月返品在庫
    @Required(fieldName = "計画6月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画6月返品在庫")
    @NumericHalfWidth(fieldName = "計画6月返品在庫")
    private Long planJunReturnInven;
    // 計画6月返品直送
    @Required(fieldName = "計画6月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画6月返品直送")
    @NumericHalfWidth(fieldName = "計画6月返品直送")
    private Long planJunReturnChokusou;
    // 計画6月返品計
    @Required(fieldName = "計画6月返品計")
    @Range(min = 1, max = 10, fieldName = "計画6月返品計")
    @NumericHalfWidth(fieldName = "計画6月返品計")
    private Long planJunReturnTotal;
    // 計画6月リベート在庫
    @Required(fieldName = "計画6月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画6月リベート在庫")
    @NumericHalfWidth(fieldName = "計画6月リベート在庫")
    private Long planJunRebateInven;
    // 計画6月リベート直送
    @Required(fieldName = "計画6月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画6月リベート直送")
    @NumericHalfWidth(fieldName = "計画6月リベート直送")
    private Long planJunRebateChokusou;
    // 計画6月リベート計
    @Required(fieldName = "計画6月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画6月リベート計")
    @NumericHalfWidth(fieldName = "計画6月リベート計")
    private Long planJunRebateTotal;
    // 計画6月センターフィ在庫
    @Required(fieldName = "計画6月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画6月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画6月センターフィ在庫")
    private Long planJunCenterFeeInven;
    // 計画6月センターフィ直送
    @Required(fieldName = "計画6月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画6月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画6月センターフィ直送")
    private Long planJunCenterFeeChokusou;
    // 計画6月センターフィ計
    @Required(fieldName = "計画6月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画6月センターフィ計")
    @NumericHalfWidth(fieldName = "計画6月センターフィ計")
    private Long planJunCenterFeeTotal;
    // 計画6月直接利益在庫
    @Required(fieldName = "計画6月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画6月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画6月直接利益在庫")
    private Long planJunChokuRiekiInven;
    // 計画6月直接利益直送
    @Required(fieldName = "計画6月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画6月直接利益直送")
    @NumericHalfWidth(fieldName = "計画6月直接利益直送")
    private Long planJunChokuRiekiChokusou;
    // 計画6月直接利益計
    @Required(fieldName = "計画6月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画6月直接利益計")
    @NumericHalfWidth(fieldName = "計画6月直接利益計")
    private Long planJunChokuRiekiTotal;
    // 計画6月直利率在庫
    @Required(fieldName = "計画6月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画6月直利率在庫")
    @NumericHalfWidth(fieldName = "計画6月直利率在庫")
    private Long planJunChokuRateInven;
    // 計画6月直利率直送
    @Required(fieldName = "計画6月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画6月直利率直送")
    @NumericHalfWidth(fieldName = "計画6月直利率直送")
    private Long planJunChokuRateChokusou;
    // 計画6月直利率計
    @Required(fieldName = "計画6月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画6月直利率計")
    @NumericHalfWidth(fieldName = "計画6月直利率計")
    private Long planJunChokuRateTotal;
    // 計画7月総売上高在庫
    @Required(fieldName = "計画7月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画7月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画7月総売上高在庫")
    private Long planJulAllSalInven;
    // 計画7月総売上高直送
    @Required(fieldName = "計画7月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画7月総売上高直送")
    @NumericHalfWidth(fieldName = "計画7月総売上高直送")
    private Long planJulAllSalChokusou;
    // 計画7月総売上高計
    @Required(fieldName = "計画7月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画7月総売上高計")
    @NumericHalfWidth(fieldName = "計画7月総売上高計")
    private Long planJulAllSalTotal;
    // 計画7月返品在庫
    @Required(fieldName = "計画7月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画7月返品在庫")
    @NumericHalfWidth(fieldName = "計画7月返品在庫")
    private Long planJulReturnInven;
    // 計画7月返品直送
    @Required(fieldName = "計画7月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画7月返品直送")
    @NumericHalfWidth(fieldName = "計画7月返品直送")
    private Long planJulReturnChokusou;
    // 計画7月返品計
    @Required(fieldName = "計画7月返品計")
    @Range(min = 1, max = 10, fieldName = "計画7月返品計")
    @NumericHalfWidth(fieldName = "計画7月返品計")
    private Long planJulReturnTotal;
    // 計画7月リベート在庫
    @Required(fieldName = "計画7月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画7月リベート在庫")
    @NumericHalfWidth(fieldName = "計画7月リベート在庫")
    private Long planJulRebateInven;
    // 計画7月リベート直送
    @Required(fieldName = "計画7月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画7月リベート直送")
    @NumericHalfWidth(fieldName = "計画7月リベート直送")
    private Long planJulRebateChokusou;
    // 計画7月リベート計
    @Required(fieldName = "計画7月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画7月リベート計")
    @NumericHalfWidth(fieldName = "計画7月リベート計")
    private Long planJulRebateTotal;
    // 計画7月センターフィ在庫
    @Required(fieldName = "計画7月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画7月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画7月センターフィ在庫")
    private Long planJulCenterFeeInven;
    // 計画7月センターフィ直送
    @Required(fieldName = "計画7月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画7月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画7月センターフィ直送")
    private Long planJulCenterFeeChokusou;
    // 計画7月センターフィ計
    @Required(fieldName = "計画7月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画7月センターフィ計")
    @NumericHalfWidth(fieldName = "計画7月センターフィ計")
    private Long planJulCenterFeeTotal;
    // 計画7月直接利益在庫
    @Required(fieldName = "計画7月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画7月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画7月直接利益在庫")
    private Long planJulChokuRiekiInven;
    // 計画7月直接利益直送
    @Required(fieldName = "計画7月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画7月直接利益直送")
    @NumericHalfWidth(fieldName = "計画7月直接利益直送")
    private Long planJulChokuRiekiChokusou;
    // 計画7月直接利益計
    @Required(fieldName = "計画7月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画7月直接利益計")
    @NumericHalfWidth(fieldName = "計画7月直接利益計")
    private Long planJulChokuRiekiTotal;
    // 計画7月直利率在庫
    @Required(fieldName = "計画7月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画7月直利率在庫")
    @NumericHalfWidth(fieldName = "計画7月直利率在庫")
    private Long planJulChokuRateInven;
    // 計画7月直利率直送
    @Required(fieldName = "計画7月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画7月直利率直送")
    @NumericHalfWidth(fieldName = "計画7月直利率直送")
    private Long planJulChokuRateChokusou;
    // 計画7月直利率計
    @Required(fieldName = "計画7月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画7月直利率計")
    @NumericHalfWidth(fieldName = "計画7月直利率計")
    private Long planJulChokuRateTotal;
    // 計画8月総売上高在庫
    @Required(fieldName = "計画8月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画8月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画8月総売上高在庫")
    private Long planAugAllSalInven;
    // 計画8月総売上高直送
    @Required(fieldName = "計画8月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画8月総売上高直送")
    @NumericHalfWidth(fieldName = "計画8月総売上高直送")
    private Long planAugAllSalChokusou;
    // 計画8月総売上高計
    @Required(fieldName = "計画8月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画8月総売上高計")
    @NumericHalfWidth(fieldName = "計画8月総売上高計")
    private Long planAugAllSalTotal;
    // 計画8月返品在庫
    @Required(fieldName = "計画8月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画8月返品在庫")
    @NumericHalfWidth(fieldName = "計画8月返品在庫")
    private Long planAugReturnInven;
    // 計画8月返品直送
    @Required(fieldName = "計画8月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画8月返品直送")
    @NumericHalfWidth(fieldName = "計画8月返品直送")
    private Long planAugReturnChokusou;
    // 計画8月返品計
    @Required(fieldName = "計画8月返品計")
    @Range(min = 1, max = 10, fieldName = "計画8月返品計")
    @NumericHalfWidth(fieldName = "計画8月返品計")
    private Long planAugReturnTotal;
    // 計画8月リベート在庫
    @Required(fieldName = "計画8月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画8月リベート在庫")
    @NumericHalfWidth(fieldName = "計画8月リベート在庫")
    private Long planAugRebateInven;
    // 計画8月リベート直送
    @Required(fieldName = "計画8月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画8月リベート直送")
    @NumericHalfWidth(fieldName = "計画8月リベート直送")
    private Long planAugRebateChokusou;
    // 計画8月リベート計
    @Required(fieldName = "計画8月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画8月リベート計")
    @NumericHalfWidth(fieldName = "計画8月リベート計")
    private Long planAugRebateTotal;
    // 計画8月センターフィ在庫
    @Required(fieldName = "計画8月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画8月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画8月センターフィ在庫")
    private Long planAugCenterFeeInven;
    // 計画8月センターフィ直送
    @Required(fieldName = "計画8月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画8月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画8月センターフィ直送")
    private Long planAugCenterFeeChokusou;
    // 計画8月センターフィ計
    @Required(fieldName = "計画8月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画8月センターフィ計")
    @NumericHalfWidth(fieldName = "計画8月センターフィ計")
    private Long planAugCenterFeeTotal;
    // 計画8月直接利益在庫
    @Required(fieldName = "計画8月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画8月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画8月直接利益在庫")
    private Long planAugChokuRiekiInven;
    // 計画8月直接利益直送
    @Required(fieldName = "計画8月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画8月直接利益直送")
    @NumericHalfWidth(fieldName = "計画8月直接利益直送")
    private Long planAugChokuRiekiChokusou;
    // 計画8月直接利益計
    @Required(fieldName = "計画8月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画8月直接利益計")
    @NumericHalfWidth(fieldName = "計画8月直接利益計")
    private Long planAugChokuRiekiTotal;
    // 計画8月直利率在庫
    @Required(fieldName = "計画8月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画8月直利率在庫")
    @NumericHalfWidth(fieldName = "計画8月直利率在庫")
    private Long planAugChokuRateInven;
    // 計画8月直利率直送
    @Required(fieldName = "計画8月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画8月直利率直送")
    @NumericHalfWidth(fieldName = "計画8月直利率直送")
    private Long planAugChokuRateChokusou;
    // 計画8月直利率計
    @Required(fieldName = "計画8月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画8月直利率計")
    @NumericHalfWidth(fieldName = "計画8月直利率計")
    private Long planAugChokuRateTotal;
    // 計画9月総売上高在庫
    @Required(fieldName = "計画9月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画9月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画9月総売上高在庫")
    private Long planSepAllSalInven;
    // 計画9月総売上高直送
    @Required(fieldName = "計画9月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画9月総売上高直送")
    @NumericHalfWidth(fieldName = "計画9月総売上高直送")
    private Long planSepAllSalChokusou;
    // 計画9月総売上高計
    @Required(fieldName = "計画9月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画9月総売上高計")
    @NumericHalfWidth(fieldName = "計画9月総売上高計")
    private Long planSepAllSalTotal;
    // 計画9月返品在庫
    @Required(fieldName = "計画9月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画9月返品在庫")
    @NumericHalfWidth(fieldName = "計画9月返品在庫")
    private Long planSepReturnInven;
    // 計画9月返品直送
    @Required(fieldName = "計画9月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画9月返品直送")
    @NumericHalfWidth(fieldName = "計画9月返品直送")
    private Long planSepReturnChokusou;
    // 計画9月返品計
    @Required(fieldName = "計画9月返品計")
    @Range(min = 1, max = 10, fieldName = "計画9月返品計")
    @NumericHalfWidth(fieldName = "計画9月返品計")
    private Long planSepReturnTotal;
    // 計画9月リベート在庫
    @Required(fieldName = "計画9月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画9月リベート在庫")
    @NumericHalfWidth(fieldName = "計画9月リベート在庫")
    private Long planSepRebateInven;
    // 計画9月リベート直送
    @Required(fieldName = "計画9月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画9月リベート直送")
    @NumericHalfWidth(fieldName = "計画9月リベート直送")
    private Long planSepRebateChokusou;
    // 計画9月リベート計
    @Required(fieldName = "計画9月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画9月リベート計")
    @NumericHalfWidth(fieldName = "計画9月リベート計")
    private Long planSepRebateTotal;
    // 計画9月センターフィ在庫
    @Required(fieldName = "計画9月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画9月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画9月センターフィ在庫")
    private Long planSepCenterFeeInven;
    // 計画9月センターフィ直送
    @Required(fieldName = "計画9月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画9月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画9月センターフィ直送")
    private Long planSepCenterFeeChokusou;
    // 計画9月センターフィ計
    @Required(fieldName = "計画9月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画9月センターフィ計")
    @NumericHalfWidth(fieldName = "計画9月センターフィ計")
    private Long planSepCenterFeeTotal;
    // 計画9月直接利益在庫
    @Required(fieldName = "計画9月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画9月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画9月直接利益在庫")
    private Long planSepChokuRiekiInven;
    // 計画9月直接利益直送
    @Required(fieldName = "計画9月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画9月直接利益直送")
    @NumericHalfWidth(fieldName = "計画9月直接利益直送")
    private Long planSepChokuRiekiChokusou;
    // 計画9月直接利益計
    @Required(fieldName = "計画9月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画9月直接利益計")
    @NumericHalfWidth(fieldName = "計画9月直接利益計")
    private Long planSepChokuRiekiTotal;
    // 計画9月直利率在庫
    @Required(fieldName = "計画9月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画9月直利率在庫")
    @NumericHalfWidth(fieldName = "計画9月直利率在庫")
    private Long planSepChokuRateInven;
    // 計画9月直利率直送
    @Required(fieldName = "計画9月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画9月直利率直送")
    @NumericHalfWidth(fieldName = "計画9月直利率直送")
    private Long planSepChokuRateChokusou;
    // 計画9月直利率計
    @Required(fieldName = "計画9月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画9月直利率計")
    @NumericHalfWidth(fieldName = "計画9月直利率計")
    private Long planSepChokuRateTotal;
    // 計画10月総売上高在庫
    @Required(fieldName = "計画10月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画10月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画10月総売上高在庫")
    private Long planOctAllSalInven;
    // 計画10月総売上高直送
    @Required(fieldName = "計画10月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画10月総売上高直送")
    @NumericHalfWidth(fieldName = "計画10月総売上高直送")
    private Long planOctAllSalChokusou;
    // 計画10月総売上高計
    @Required(fieldName = "計画10月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画10月総売上高計")
    @NumericHalfWidth(fieldName = "計画10月総売上高計")
    private Long planOctAllSalTotal;
    // 計画10月返品在庫
    @Required(fieldName = "計画10月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画10月返品在庫")
    @NumericHalfWidth(fieldName = "計画10月返品在庫")
    private Long planOctReturnInven;
    // 計画10月返品直送
    @Required(fieldName = "計画10月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画10月返品直送")
    @NumericHalfWidth(fieldName = "計画10月返品直送")
    private Long planOctReturnChokusou;
    // 計画10月返品計
    @Required(fieldName = "計画10月返品計")
    @Range(min = 1, max = 10, fieldName = "計画10月返品計")
    @NumericHalfWidth(fieldName = "計画10月返品計")
    private Long planOctReturnTotal;
    // 計画10月リベート在庫
    @Required(fieldName = "計画10月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画10月リベート在庫")
    @NumericHalfWidth(fieldName = "計画10月リベート在庫")
    private Long planOctRebateInven;
    // 計画10月リベート直送
    @Required(fieldName = "計画10月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画10月リベート直送")
    @NumericHalfWidth(fieldName = "計画10月リベート直送")
    private Long planOctRebateChokusou;
    // 計画10月リベート計
    @Required(fieldName = "計画10月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画10月リベート計")
    @NumericHalfWidth(fieldName = "計画10月リベート計")
    private Long planOctRebateTotal;
    // 計画10月センターフィ在庫
    @Required(fieldName = "計画10月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画10月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画10月センターフィ在庫")
    private Long planOctCenterFeeInven;
    // 計画10月センターフィ直送
    @Required(fieldName = "計画10月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画10月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画10月センターフィ直送")
    private Long planOctCenterFeeChokusou;
    // 計画10月センターフィ計
    @Required(fieldName = "計画10月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画10月センターフィ計")
    @NumericHalfWidth(fieldName = "計画10月センターフィ計")
    private Long planOctCenterFeeTotal;
    // 計画10月直接利益在庫
    @Required(fieldName = "計画10月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画10月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画10月直接利益在庫")
    private Long planOctChokuRiekiInven;
    // 計画10月直接利益直送
    @Required(fieldName = "計画10月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画10月直接利益直送")
    @NumericHalfWidth(fieldName = "計画10月直接利益直送")
    private Long planOctChokuRiekiChokusou;
    // 計画10月直接利益計
    @Required(fieldName = "計画10月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画10月直接利益計")
    @NumericHalfWidth(fieldName = "計画10月直接利益計")
    private Long planOctChokuRiekiTotal;
    // 計画10月直利率在庫
    @Required(fieldName = "計画10月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画10月直利率在庫")
    @NumericHalfWidth(fieldName = "計画10月直利率在庫")
    private Long planOctChokuRateInven;
    // 計画10月直利率直送
    @Required(fieldName = "計画10月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画10月直利率直送")
    @NumericHalfWidth(fieldName = "計画10月直利率直送")
    private Long planOctChokuRateChokusou;
    // 計画10月直利率計
    @Required(fieldName = "計画10月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画10月直利率計")
    @NumericHalfWidth(fieldName = "計画10月直利率計")
    private Long planOctChokuRateTotal;
    // 計画11月総売上高在庫
    @Required(fieldName = "計画11月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画11月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画11月総売上高在庫")
    private Long planNovAllSalInven;
    // 計画11月総売上高直送
    @Required(fieldName = "計画11月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画11月総売上高直送")
    @NumericHalfWidth(fieldName = "計画11月総売上高直送")
    private Long planNovAllSalChokusou;
    // 計画11月総売上高計
    @Required(fieldName = "計画11月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画11月総売上高計")
    @NumericHalfWidth(fieldName = "計画11月総売上高計")
    private Long planNovAllSalTotal;
    // 計画11月返品在庫
    @Required(fieldName = "計画11月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画11月返品在庫")
    @NumericHalfWidth(fieldName = "計画11月返品在庫")
    private Long planNovReturnInven;
    // 計画11月返品直送
    @Required(fieldName = "計画11月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画11月返品直送")
    @NumericHalfWidth(fieldName = "計画11月返品直送")
    private Long planNovReturnChokusou;
    // 計画11月返品計
    @Required(fieldName = "計画11月返品計")
    @Range(min = 1, max = 10, fieldName = "計画11月返品計")
    @NumericHalfWidth(fieldName = "計画11月返品計")
    private Long planNovReturnTotal;
    // 計画11月リベート在庫
    @Required(fieldName = "計画11月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画11月リベート在庫")
    @NumericHalfWidth(fieldName = "計画11月リベート在庫")
    private Long planNovRebateInven;
    // 計画11月リベート直送
    @Required(fieldName = "計画11月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画11月リベート直送")
    @NumericHalfWidth(fieldName = "計画11月リベート直送")
    private Long planNovRebateChokusou;
    // 計画11月リベート計
    @Required(fieldName = "計画11月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画11月リベート計")
    @NumericHalfWidth(fieldName = "計画11月リベート計")
    private Long planNovRebateTotal;
    // 計画11月センターフィ在庫
    @Required(fieldName = "計画11月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画11月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画11月センターフィ在庫")
    private Long planNovCenterFeeInven;
    // 計画11月センターフィ直送
    @Required(fieldName = "計画11月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画11月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画11月センターフィ直送")
    private Long planNovCenterFeeChokusou;
    // 計画11月センターフィ計
    @Required(fieldName = "計画11月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画11月センターフィ計")
    @NumericHalfWidth(fieldName = "計画11月センターフィ計")
    private Long planNovCenterFeeTotal;
    // 計画11月直接利益在庫
    @Required(fieldName = "計画11月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画11月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画11月直接利益在庫")
    private Long planNovChokuRiekiInven;
    // 計画11月直接利益直送
    @Required(fieldName = "計画11月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画11月直接利益直送")
    @NumericHalfWidth(fieldName = "計画11月直接利益直送")
    private Long planNovChokuRiekiChokusou;
    // 計画11月直接利益計
    @Required(fieldName = "計画11月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画11月直接利益計")
    @NumericHalfWidth(fieldName = "計画11月直接利益計")
    private Long planNovChokuRiekiTotal;
    // 計画11月直利率在庫
    @Required(fieldName = "計画11月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画11月直利率在庫")
    @NumericHalfWidth(fieldName = "計画11月直利率在庫")
    private Long planNovChokuRateInven;
    // 計画11月直利率直送
    @Required(fieldName = "計画11月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画11月直利率直送")
    @NumericHalfWidth(fieldName = "計画11月直利率直送")
    private Long planNovChokuRateChokusou;
    // 計画11月直利率計
    @Required(fieldName = "計画11月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画11月直利率計")
    @NumericHalfWidth(fieldName = "計画11月直利率計")
    private Long planNovChokuRateTotal;
    // 計画12月総売上高在庫
    @Required(fieldName = "計画12月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画12月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画12月総売上高在庫")
    private Long planDecAllSalInven;
    // 計画12月総売上高直送
    @Required(fieldName = "計画12月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画12月総売上高直送")
    @NumericHalfWidth(fieldName = "計画12月総売上高直送")
    private Long planDecAllSalChokusou;
    // 計画12月総売上高計
    @Required(fieldName = "計画12月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画12月総売上高計")
    @NumericHalfWidth(fieldName = "計画12月総売上高計")
    private Long planDecAllSalTotal;
    // 計画12月返品在庫
    @Required(fieldName = "計画12月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画12月返品在庫")
    @NumericHalfWidth(fieldName = "計画12月返品在庫")
    private Long planDecReturnInven;
    // 計画12月返品直送
    @Required(fieldName = "計画12月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画12月返品直送")
    @NumericHalfWidth(fieldName = "計画12月返品直送")
    private Long planDecReturnChokusou;
    // 計画12月返品計
    @Required(fieldName = "計画12月返品計")
    @Range(min = 1, max = 10, fieldName = "計画12月返品計")
    @NumericHalfWidth(fieldName = "計画12月返品計")
    private Long planDecReturnTotal;
    // 計画12月リベート在庫
    @Required(fieldName = "計画12月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画12月リベート在庫")
    @NumericHalfWidth(fieldName = "計画12月リベート在庫")
    private Long planDecRebateInven;
    // 計画12月リベート直送
    @Required(fieldName = "計画12月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画12月リベート直送")
    @NumericHalfWidth(fieldName = "計画12月リベート直送")
    private Long planDecRebateChokusou;
    // 計画12月リベート計
    @Required(fieldName = "計画12月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画12月リベート計")
    @NumericHalfWidth(fieldName = "計画12月リベート計")
    private Long planDecRebateTotal;
    // 計画12月センターフィ在庫
    @Required(fieldName = "計画12月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画12月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画12月センターフィ在庫")
    private Long planDecCenterFeeInven;
    // 計画12月センターフィ直送
    @Required(fieldName = "計画12月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画12月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画12月センターフィ直送")
    private Long planDecCenterFeeChokusou;
    // 計画12月センターフィ計
    @Required(fieldName = "計画12月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画12月センターフィ計")
    @NumericHalfWidth(fieldName = "計画12月センターフィ計")
    private Long planDecCenterFeeTotal;
    // 計画12月直接利益在庫
    @Required(fieldName = "計画12月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画12月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画12月直接利益在庫")
    private Long planDecChokuRiekiInven;
    // 計画12月直接利益直送
    @Required(fieldName = "計画12月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画12月直接利益直送")
    @NumericHalfWidth(fieldName = "計画12月直接利益直送")
    private Long planDecChokuRiekiChokusou;
    // 計画12月直接利益計
    @Required(fieldName = "計画12月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画12月直接利益計")
    @NumericHalfWidth(fieldName = "計画12月直接利益計")
    private Long planDecChokuRiekiTotal;
    // 計画12月直利率在庫
    @Required(fieldName = "計画12月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画12月直利率在庫")
    @NumericHalfWidth(fieldName = "計画12月直利率在庫")
    private Long planDecChokuRateInven;
    // 計画12月直利率直送
    @Required(fieldName = "計画12月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画12月直利率直送")
    @NumericHalfWidth(fieldName = "計画12月直利率直送")
    private Long planDecChokuRateChokusou;
    // 計画12月直利率計
    @Required(fieldName = "計画12月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画12月直利率計")
    @NumericHalfWidth(fieldName = "計画12月直利率計")
    private Long planDecChokuRateTotal;
    // 計画1月総売上高在庫
    @Required(fieldName = "計画1月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画1月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画1月総売上高在庫")
    private Long planJanAllSalInven;
    // 計画1月総売上高直送
    @Required(fieldName = "計画1月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画1月総売上高直送")
    @NumericHalfWidth(fieldName = "計画1月総売上高直送")
    private Long planJanAllSalChokusou;
    // 計画1月総売上高計
    @Required(fieldName = "計画1月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画1月総売上高計")
    @NumericHalfWidth(fieldName = "計画1月総売上高計")
    private Long planJanAllSalTotal;
    // 計画1月返品在庫
    @Required(fieldName = "計画1月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画1月返品在庫")
    @NumericHalfWidth(fieldName = "計画1月返品在庫")
    private Long planJanReturnInven;
    // 計画1月返品直送
    @Required(fieldName = "計画1月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画1月返品直送")
    @NumericHalfWidth(fieldName = "計画1月返品直送")
    private Long planJanReturnChokusou;
    // 計画1月返品計
    @Required(fieldName = "計画1月返品計")
    @Range(min = 1, max = 10, fieldName = "計画1月返品計")
    @NumericHalfWidth(fieldName = "計画1月返品計")
    private Long planJanReturnTotal;
    // 計画1月リベート在庫
    @Required(fieldName = "計画1月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画1月リベート在庫")
    @NumericHalfWidth(fieldName = "計画1月リベート在庫")
    private Long planJanRebateInven;
    // 計画1月リベート直送
    @Required(fieldName = "計画1月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画1月リベート直送")
    @NumericHalfWidth(fieldName = "計画1月リベート直送")
    private Long planJanRebateChokusou;
    // 計画1月リベート計
    @Required(fieldName = "計画1月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画1月リベート計")
    @NumericHalfWidth(fieldName = "計画1月リベート計")
    private Long planJanRebateTotal;
    // 計画1月センターフィ在庫
    @Required(fieldName = "計画1月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画1月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画1月センターフィ在庫")
    private Long planJanCenterFeeInven;
    // 計画1月センターフィ直送
    @Required(fieldName = "計画1月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画1月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画1月センターフィ直送")
    private Long planJanCenterFeeChokusou;
    // 計画1月センターフィ計
    @Required(fieldName = "計画1月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画1月センターフィ計")
    @NumericHalfWidth(fieldName = "計画1月センターフィ計")
    private Long planJanCenterFeeTotal;
    // 計画1月直接利益在庫
    @Required(fieldName = "計画1月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画1月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画1月直接利益在庫")
    private Long planJanChokuRiekiInven;
    // 計画1月直接利益直送
    @Required(fieldName = "計画1月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画1月直接利益直送")
    @NumericHalfWidth(fieldName = "計画1月直接利益直送")
    private Long planJanChokuRiekiChokusou;
    // 計画1月直接利益計
    @Required(fieldName = "計画1月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画1月直接利益計")
    @NumericHalfWidth(fieldName = "計画1月直接利益計")
    private Long planJanChokuRiekiTotal;
    // 計画1月直利率在庫
    @Required(fieldName = "計画1月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画1月直利率在庫")
    @NumericHalfWidth(fieldName = "計画1月直利率在庫")
    private Long planJanChokuRateInven;
    // 計画1月直利率直送
    @Required(fieldName = "計画1月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画1月直利率直送")
    @NumericHalfWidth(fieldName = "計画1月直利率直送")
    private Long planJanChokuRateChokusou;
    // 計画1月直利率計
    @Required(fieldName = "計画1月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画1月直利率計")
    @NumericHalfWidth(fieldName = "計画1月直利率計")
    private Long planJanChokuRateTotal;
    // 計画2月総売上高在庫
    @Required(fieldName = "計画2月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画2月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画2月総売上高在庫")
    private Long planFebAllSalInven;
    // 計画2月総売上高直送
    @Required(fieldName = "計画2月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画2月総売上高直送")
    @NumericHalfWidth(fieldName = "計画2月総売上高直送")
    private Long planFebAllSalChokusou;
    // 計画2月総売上高計
    @Required(fieldName = "計画2月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画2月総売上高計")
    @NumericHalfWidth(fieldName = "計画2月総売上高計")
    private Long planFebAllSalTotal;
    // 計画2月返品在庫
    @Required(fieldName = "計画2月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画2月返品在庫")
    @NumericHalfWidth(fieldName = "計画2月返品在庫")
    private Long planFebReturnInven;
    // 計画2月返品直送
    @Required(fieldName = "計画2月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画2月返品直送")
    @NumericHalfWidth(fieldName = "計画2月返品直送")
    private Long planFebReturnChokusou;
    // 計画2月返品計
    @Required(fieldName = "計画2月返品計")
    @Range(min = 1, max = 10, fieldName = "計画2月返品計")
    @NumericHalfWidth(fieldName = "計画2月返品計")
    private Long planFebReturnTotal;
    // 計画2月リベート在庫
    @Required(fieldName = "計画2月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画2月リベート在庫")
    @NumericHalfWidth(fieldName = "計画2月リベート在庫")
    private Long planFebRebateInven;
    // 計画2月リベート直送
    @Required(fieldName = "計画2月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画2月リベート直送")
    @NumericHalfWidth(fieldName = "計画2月リベート直送")
    private Long planFebRebateChokusou;
    // 計画2月リベート計
    @Required(fieldName = "計画2月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画2月リベート計")
    @NumericHalfWidth(fieldName = "計画2月リベート計")
    private Long planFebRebateTotal;
    // 計画2月センターフィ在庫
    @Required(fieldName = "計画2月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画2月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画2月センターフィ在庫")
    private Long planFebCenterFeeInven;
    // 計画2月センターフィ直送
    @Required(fieldName = "計画2月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画2月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画2月センターフィ直送")
    private Long planFebCenterFeeChokusou;
    // 計画2月センターフィ計
    @Required(fieldName = "計画2月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画2月センターフィ計")
    @NumericHalfWidth(fieldName = "計画2月センターフィ計")
    private Long planFebCenterFeeTotal;
    // 計画2月直接利益在庫
    @Required(fieldName = "計画2月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画2月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画2月直接利益在庫")
    private Long planFebChokuRiekiInven;
    // 計画2月直接利益直送
    @Required(fieldName = "計画2月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画2月直接利益直送")
    @NumericHalfWidth(fieldName = "計画2月直接利益直送")
    private Long planFebChokuRiekiChokusou;
    // 計画2月直接利益計
    @Required(fieldName = "計画2月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画2月直接利益計")
    @NumericHalfWidth(fieldName = "計画2月直接利益計")
    private Long planFebChokuRiekiTotal;
    // 計画2月直利率在庫
    @Required(fieldName = "計画2月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画2月直利率在庫")
    @NumericHalfWidth(fieldName = "計画2月直利率在庫")
    private Long planFebChokuRateInven;
    // 計画2月直利率直送
    @Required(fieldName = "計画2月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画2月直利率直送")
    @NumericHalfWidth(fieldName = "計画2月直利率直送")
    private Long planFebChokuRateChokusou;
    // 計画2月直利率計
    @Required(fieldName = "計画2月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画2月直利率計")
    @NumericHalfWidth(fieldName = "計画2月直利率計")
    private Long planFebChokuRateTotal;
    // 計画3月総売上高在庫
    @Required(fieldName = "計画3月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "計画3月総売上高在庫")
    @NumericHalfWidth(fieldName = "計画3月総売上高在庫")
    private Long planMarAllSalInven;
    // 計画3月総売上高直送
    @Required(fieldName = "計画3月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "計画3月総売上高直送")
    @NumericHalfWidth(fieldName = "計画3月総売上高直送")
    private Long planMarAllSalChokusou;
    // 計画3月総売上高計
    @Required(fieldName = "計画3月総売上高計")
    @Range(min = 1, max = 10, fieldName = "計画3月総売上高計")
    @NumericHalfWidth(fieldName = "計画3月総売上高計")
    private Long planMarAllSalTotal;
    // 計画3月返品在庫
    @Required(fieldName = "計画3月返品在庫")
    @Range(min = 1, max = 10, fieldName = "計画3月返品在庫")
    @NumericHalfWidth(fieldName = "計画3月返品在庫")
    private Long planMarReturnInven;
    // 計画3月返品直送
    @Required(fieldName = "計画3月返品直送")
    @Range(min = 1, max = 10, fieldName = "計画3月返品直送")
    @NumericHalfWidth(fieldName = "計画3月返品直送")
    private Long planMarReturnChokusou;
    // 計画3月返品計
    @Required(fieldName = "計画3月返品計")
    @Range(min = 1, max = 10, fieldName = "計画3月返品計")
    @NumericHalfWidth(fieldName = "計画3月返品計")
    private Long planMarReturnTotal;
    // 計画3月リベート在庫
    @Required(fieldName = "計画3月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "計画3月リベート在庫")
    @NumericHalfWidth(fieldName = "計画3月リベート在庫")
    private Long planMarRebateInven;
    // 計画3月リベート直送
    @Required(fieldName = "計画3月リベート直送")
    @Range(min = 1, max = 10, fieldName = "計画3月リベート直送")
    @NumericHalfWidth(fieldName = "計画3月リベート直送")
    private Long planMarRebateChokusou;
    // 計画3月リベート計
    @Required(fieldName = "計画3月リベート計")
    @Range(min = 1, max = 10, fieldName = "計画3月リベート計")
    @NumericHalfWidth(fieldName = "計画3月リベート計")
    private Long planMarRebateTotal;
    // 計画3月センターフィ在庫
    @Required(fieldName = "計画3月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "計画3月センターフィ在庫")
    @NumericHalfWidth(fieldName = "計画3月センターフィ在庫")
    private Long planMarCenterFeeInven;
    // 計画3月センターフィ直送
    @Required(fieldName = "計画3月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "計画3月センターフィ直送")
    @NumericHalfWidth(fieldName = "計画3月センターフィ直送")
    private Long planMarCenterFeeChokusou;
    // 計画3月センターフィ計
    @Required(fieldName = "計画3月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "計画3月センターフィ計")
    @NumericHalfWidth(fieldName = "計画3月センターフィ計")
    private Long planMarCenterFeeTotal;
    // 計画3月直接利益在庫
    @Required(fieldName = "計画3月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "計画3月直接利益在庫")
    @NumericHalfWidth(fieldName = "計画3月直接利益在庫")
    private Long planMarChokuRiekiInven;
    // 計画3月直接利益直送
    @Required(fieldName = "計画3月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "計画3月直接利益直送")
    @NumericHalfWidth(fieldName = "計画3月直接利益直送")
    private Long planMarChokuRiekiChokusou;
    // 計画3月直接利益計
    @Required(fieldName = "計画3月直接利益計")
    @Range(min = 1, max = 10, fieldName = "計画3月直接利益計")
    @NumericHalfWidth(fieldName = "計画3月直接利益計")
    private Long planMarChokuRiekiTotal;
    // 計画3月直利率在庫
    @Required(fieldName = "計画3月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "計画3月直利率在庫")
    @NumericHalfWidth(fieldName = "計画3月直利率在庫")
    private Long planMarChokuRateInven;
    // 計画3月直利率直送
    @Required(fieldName = "計画3月直利率直送")
    @Range(min = 1, max = 10, fieldName = "計画3月直利率直送")
    @NumericHalfWidth(fieldName = "計画3月直利率直送")
    private Long planMarChokuRateChokusou;
    // 計画3月直利率計
    @Required(fieldName = "計画3月直利率計")
    @Range(min = 1, max = 10, fieldName = "計画3月直利率計")
    @NumericHalfWidth(fieldName = "計画3月直利率計")
    private Long planMarChokuRateTotal;



    // 実績見通し1月総売上高在庫
    @Required(fieldName = "実績見通し1月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月総売上高在庫")
    @NumericHalfWidth(fieldName = "実績見通し1月総売上高在庫")
    private Long outlookJanAllSalInven;
    // 実績見通し1月総売上高直送
    @Required(fieldName = "実績見通し1月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月総売上高直送")
    @NumericHalfWidth(fieldName = "実績見通し1月総売上高直送")
    private Long outlookJanAllSalChokusou;
    // 実績見通し1月総売上高計
    @Required(fieldName = "実績見通し1月総売上高計")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月総売上高計")
    @NumericHalfWidth(fieldName = "実績見通し1月総売上高計")
    private Long outlookJanAllSalTotal;
    // 実績見通し1月返品在庫
    @Required(fieldName = "実績見通し1月返品在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月返品在庫")
    @NumericHalfWidth(fieldName = "実績見通し1月返品在庫")
    private Long outlookJanReturnInven;
    // 実績見通し1月返品直送
    @Required(fieldName = "実績見通し1月返品直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月返品直送")
    @NumericHalfWidth(fieldName = "実績見通し1月返品直送")
    private Long outlookJanReturnChokusou;
    // 実績見通し1月返品計
    @Required(fieldName = "実績見通し1月返品計")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月返品計")
    @NumericHalfWidth(fieldName = "実績見通し1月返品計")
    private Long outlookJanReturnTotal;
    // 実績見通し1月リベート在庫
    @Required(fieldName = "実績見通し1月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月リベート在庫")
    @NumericHalfWidth(fieldName = "実績見通し1月リベート在庫")
    private Long outlookJanRebateInven;
    // 実績見通し1月リベート直送
    @Required(fieldName = "実績見通し1月リベート直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月リベート直送")
    @NumericHalfWidth(fieldName = "実績見通し1月リベート直送")
    private Long outlookJanRebateChokusou;
    // 実績見通し1月リベート計
    @Required(fieldName = "実績見通し1月リベート計")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月リベート計")
    @NumericHalfWidth(fieldName = "実績見通し1月リベート計")
    private Long outlookJanRebateTotal;
    // 実績見通し1月センターフィ在庫
    @Required(fieldName = "実績見通し1月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月センターフィ在庫")
    @NumericHalfWidth(fieldName = "実績見通し1月センターフィ在庫")
    private Long outlookJanCenterFeeInven;
    // 実績見通し1月センターフィ直送
    @Required(fieldName = "実績見通し1月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月センターフィ直送")
    @NumericHalfWidth(fieldName = "実績見通し1月センターフィ直送")
    private Long outlookJanCenterFeeChokusou;
    // 実績見通し1月センターフィ計
    @Required(fieldName = "実績見通し1月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月センターフィ計")
    @NumericHalfWidth(fieldName = "実績見通し1月センターフィ計")
    private Long outlookJanCenterFeeTotal;
    // 実績見通し1月直接利益在庫
    @Required(fieldName = "実績見通し1月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月直接利益在庫")
    @NumericHalfWidth(fieldName = "実績見通し1月直接利益在庫")
    private Long outlookJanChokuRiekiInven;
    // 実績見通し1月直接利益直送
    @Required(fieldName = "実績見通し1月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月直接利益直送")
    @NumericHalfWidth(fieldName = "実績見通し1月直接利益直送")
    private Long outlookJanChokuRiekiChokusou;
    // 実績見通し1月直接利益計
    @Required(fieldName = "実績見通し1月直接利益計")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月直接利益計")
    @NumericHalfWidth(fieldName = "実績見通し1月直接利益計")
    private Long outlookJanChokuRiekiTotal;
    // 実績見通し1月直利率在庫
    @Required(fieldName = "実績見通し1月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月直利率在庫")
    @NumericHalfWidth(fieldName = "実績見通し1月直利率在庫")
    private Long outlookJanChokuRateInven;
    // 実績見通し1月直利率直送
    @Required(fieldName = "実績見通し1月直利率直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月直利率直送")
    @NumericHalfWidth(fieldName = "実績見通し1月直利率直送")
    private Long outlookJanChokuRateChokusou;
    // 実績見通し1月直利率計
    @Required(fieldName = "実績見通し1月直利率計")
    @Range(min = 1, max = 10, fieldName = "実績見通し1月直利率計")
    @NumericHalfWidth(fieldName = "実績見通し1月直利率計")
    private Long outlookJanChokuRateTotal;
    // 実績見通し2月総売上高在庫
    @Required(fieldName = "実績見通し2月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月総売上高在庫")
    @NumericHalfWidth(fieldName = "実績見通し2月総売上高在庫")
    private Long outlookFebAllSalInven;
    // 実績見通し2月総売上高直送
    @Required(fieldName = "実績見通し2月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月総売上高直送")
    @NumericHalfWidth(fieldName = "実績見通し2月総売上高直送")
    private Long outlookFebAllSalChokusou;
    // 実績見通し2月総売上高計
    @Required(fieldName = "実績見通し2月総売上高計")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月総売上高計")
    @NumericHalfWidth(fieldName = "実績見通し2月総売上高計")
    private Long outlookFebAllSalTotal;
    // 実績見通し2月返品在庫
    @Required(fieldName = "実績見通し2月返品在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月返品在庫")
    @NumericHalfWidth(fieldName = "実績見通し2月返品在庫")
    private Long outlookFebReturnInven;
    // 実績見通し2月返品直送
    @Required(fieldName = "実績見通し2月返品直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月返品直送")
    @NumericHalfWidth(fieldName = "実績見通し2月返品直送")
    private Long outlookFebReturnChokusou;
    // 実績見通し2月返品計
    @Required(fieldName = "実績見通し2月返品計")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月返品計")
    @NumericHalfWidth(fieldName = "実績見通し2月返品計")
    private Long outlookFebReturnTotal;
    // 実績見通し2月リベート在庫
    @Required(fieldName = "実績見通し2月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月リベート在庫")
    @NumericHalfWidth(fieldName = "実績見通し2月リベート在庫")
    private Long outlookFebRebateInven;
    // 実績見通し2月リベート直送
    @Required(fieldName = "実績見通し2月リベート直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月リベート直送")
    @NumericHalfWidth(fieldName = "実績見通し2月リベート直送")
    private Long outlookFebRebateChokusou;
    // 実績見通し2月リベート計
    @Required(fieldName = "実績見通し2月リベート計")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月リベート計")
    @NumericHalfWidth(fieldName = "実績見通し2月リベート計")
    private Long outlookFebRebateTotal;
    // 実績見通し2月センターフィ在庫
    @Required(fieldName = "実績見通し2月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月センターフィ在庫")
    @NumericHalfWidth(fieldName = "実績見通し2月センターフィ在庫")
    private Long outlookFebCenterFeeInven;
    // 実績見通し2月センターフィ直送
    @Required(fieldName = "実績見通し2月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月センターフィ直送")
    @NumericHalfWidth(fieldName = "実績見通し2月センターフィ直送")
    private Long outlookFebCenterFeeChokusou;
    // 実績見通し2月センターフィ計
    @Required(fieldName = "実績見通し2月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月センターフィ計")
    @NumericHalfWidth(fieldName = "実績見通し2月センターフィ計")
    private Long outlookFebCenterFeeTotal;
    // 実績見通し2月直接利益在庫
    @Required(fieldName = "実績見通し2月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月直接利益在庫")
    @NumericHalfWidth(fieldName = "実績見通し2月直接利益在庫")
    private Long outlookFebChokuRiekiInven;
    // 実績見通し2月直接利益直送
    @Required(fieldName = "実績見通し2月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月直接利益直送")
    @NumericHalfWidth(fieldName = "実績見通し2月直接利益直送")
    private Long outlookFebChokuRiekiChokusou;
    // 実績見通し2月直接利益計
    @Required(fieldName = "実績見通し2月直接利益計")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月直接利益計")
    @NumericHalfWidth(fieldName = "実績見通し2月直接利益計")
    private Long outlookFebChokuRiekiTotal;
    // 実績見通し2月直利率在庫
    @Required(fieldName = "実績見通し2月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月直利率在庫")
    @NumericHalfWidth(fieldName = "実績見通し2月直利率在庫")
    private Long outlookFebChokuRateInven;
    // 実績見通し2月直利率直送
    @Required(fieldName = "実績見通し2月直利率直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月直利率直送")
    @NumericHalfWidth(fieldName = "実績見通し2月直利率直送")
    private Long outlookFebChokuRateChokusou;
    // 実績見通し2月直利率計
    @Required(fieldName = "実績見通し2月直利率計")
    @Range(min = 1, max = 10, fieldName = "実績見通し2月直利率計")
    @NumericHalfWidth(fieldName = "実績見通し2月直利率計")
    private Long outlookFebChokuRateTotal;
    // 実績見通し3月総売上高在庫
    @Required(fieldName = "実績見通し3月総売上高在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月総売上高在庫")
    @NumericHalfWidth(fieldName = "実績見通し3月総売上高在庫")
    private Long outlookMarAllSalInven;
    // 実績見通し3月総売上高直送
    @Required(fieldName = "実績見通し3月総売上高直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月総売上高直送")
    @NumericHalfWidth(fieldName = "実績見通し3月総売上高直送")
    private Long outlookMarAllSalChokusou;
    // 実績見通し3月総売上高計
    @Required(fieldName = "実績見通し3月総売上高計")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月総売上高計")
    @NumericHalfWidth(fieldName = "実績見通し3月総売上高計")
    private Long outlookMarAllSalTotal;
    // 実績見通し3月返品在庫
    @Required(fieldName = "実績見通し3月返品在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月返品在庫")
    @NumericHalfWidth(fieldName = "実績見通し3月返品在庫")
    private Long outlookMarReturnInven;
    // 実績見通し3月返品直送
    @Required(fieldName = "実績見通し3月返品直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月返品直送")
    @NumericHalfWidth(fieldName = "実績見通し3月返品直送")
    private Long outlookMarReturnChokusou;
    // 実績見通し3月返品計
    @Required(fieldName = "実績見通し3月返品計")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月返品計")
    @NumericHalfWidth(fieldName = "実績見通し3月返品計")
    private Long outlookMarReturnTotal;
    // 実績見通し3月リベート在庫
    @Required(fieldName = "実績見通し3月リベート在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月リベート在庫")
    @NumericHalfWidth(fieldName = "実績見通し3月リベート在庫")
    private Long outlookMarRebateInven;
    // 実績見通し3月リベート直送
    @Required(fieldName = "実績見通し3月リベート直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月リベート直送")
    @NumericHalfWidth(fieldName = "実績見通し3月リベート直送")
    private Long outlookMarRebateChokusou;
    // 実績見通し3月リベート計
    @Required(fieldName = "実績見通し3月リベート計")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月リベート計")
    @NumericHalfWidth(fieldName = "実績見通し3月リベート計")
    private Long outlookMarRebateTotal;
    // 実績見通し3月センターフィ在庫
    @Required(fieldName = "実績見通し3月センターフィ在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月センターフィ在庫")
    @NumericHalfWidth(fieldName = "実績見通し3月センターフィ在庫")
    private Long outlookMarCenterFeeInven;
    // 実績見通し3月センターフィ直送
    @Required(fieldName = "実績見通し3月センターフィ直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月センターフィ直送")
    @NumericHalfWidth(fieldName = "実績見通し3月センターフィ直送")
    private Long outlookMarCenterFeeChokusou;
    // 実績見通し3月センターフィ計
    @Required(fieldName = "実績見通し3月センターフィ計")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月センターフィ計")
    @NumericHalfWidth(fieldName = "実績見通し3月センターフィ計")
    private Long outlookMarCenterFeeTotal;
    // 実績見通し3月直接利益在庫
    @Required(fieldName = "実績見通し3月直接利益在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月直接利益在庫")
    @NumericHalfWidth(fieldName = "実績見通し3月直接利益在庫")
    private Long outlookMarChokuRiekiInven;
    // 実績見通し3月直接利益直送
    @Required(fieldName = "実績見通し3月直接利益直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月直接利益直送")
    @NumericHalfWidth(fieldName = "実績見通し3月直接利益直送")
    private Long outlookMarChokuRiekiChokusou;
    // 実績見通し3月直接利益計
    @Required(fieldName = "実績見通し3月直接利益計")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月直接利益計")
    @NumericHalfWidth(fieldName = "実績見通し3月直接利益計")
    private Long outlookMarChokuRiekiTotal;
    // 実績見通し3月直利率在庫
    @Required(fieldName = "実績見通し3月直利率在庫")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月直利率在庫")
    @NumericHalfWidth(fieldName = "実績見通し3月直利率在庫")
    private Long outlookMarChokuRateInven;
    // 実績見通し3月直利率直送
    @Required(fieldName = "実績見通し3月直利率直送")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月直利率直送")
    @NumericHalfWidth(fieldName = "実績見通し3月直利率直送")
    private Long outlookMarChokuRateChokusou;
    // 実績見通し3月直利率計
    @Required(fieldName = "実績見通し3月直利率計")
    @Range(min = 1, max = 10, fieldName = "実績見通し3月直利率計")
    @NumericHalfWidth(fieldName = "実績見通し3月直利率計")
    private Long outlookMarChokuRateTotal;


    /**
     * データタイプ   1:次年度計画マスタ
     *             2:見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜本社＞
     *             3:見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜エリア＞
     *             4:間接利益計画_メーカー別
     */
    private String dataType;

    /**
     * エリアリスト   画面選択されたエリアのエリアコードリスト
     *              採算管理単位計画策定エリアが選択された場合は"SKSA"
     *              複数のエリアを指定可能
     */
    private List<AreaInfo> areaList;

    /**
     * 本社場所区分  0:本社
     *             1:場所
     */
    private String hnshBashoKubun;

    /**
     * データ区分リスト  0:移管前（当年度組織）
     *                1:移管後（次年度組織）
     *                複数のデータ区分を指定可能
     */
    private List<String> dataKubun;

    // ==================== 便利メソッド ====================
    /**
     * データ区分リストをカンマ区切り文字列として取得
     * @return カンマ区切りのデータ区分文字列
     */
    public String getDataKubunString() {
        if (dataKubun == null || dataKubun.isEmpty()) {
            return null;
        }
        return String.join(",", dataKubun);
    }

    @Override
    public Map<String, Object> toDatabaseFieldsCore(boolean isInsert) {
        Map<String, Object> fields = new HashMap<>();

        if (isInsert) {
            // 挿入時のみ設定するフィールド
            // 年度（次年度を自動設定）
            fields.put("NENDO", DateUtil.getNextFiscalYear());
            fields.put("GROUP_CODE", groupCode);
            fields.put("SSNKN_TNCD", saisanCode);
        }

        // 基本フィールド
        fields.put("AREA_CODE", areaCode);
        fields.put("UNIT_CODE", unitCode);
        fields.put("TNTSH_MEI_KANJI", tantoshaName);
        fields.put("KIGYO_CODE", kigyoCode);
        fields.put("SUB_CTGRY_MEI_TNSHK_KANA", kategori);
        fields.put("SUB_CTGRY_MEI_TNSHK_KANJI", subKategori);
        fields.put("GYT_MEI", gyotai);
        fields.put("TRKM_KUBUN", afterTorikumKbn);
        fields.put("GYT_HRTS", gyotaiHiritsu);

        // 各種金額
        // 計画＿在庫売上＿１月目
        fields.put("KKK_ZAIKO_URG_1_TSKM", planAprAllSalInven * 1000);
        // 計画＿直送売上＿１月目
        fields.put("KKK_CHKS_URG_1_TSKM", planAprAllSalChokusou * 1000);
        // 計画＿在庫返品等＿１月目
        fields.put("KKK_ZAIKO_HMPNT_1_TSKM", planAprReturnInven * 1000);
        // 計画＿直送返品等＿１月目
        fields.put("KKK_CHKS_HMPNT_1_TSKM", planAprReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿１月目
        fields.put("KKK_ZAIKO_SHHR_RBT_1_TSKM", planAprRebateInven * 1000);
        // 計画＿直送支払リベート＿１月目
        fields.put("KKK_CHKS_SHHR_RBT_1_TSKM", planAprRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿１月目
        fields.put("KKK_ZAIKO_CNTR_FEE_1_TSKM", planAprCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿１月目
        fields.put("KKK_CHKS_CNTR_FEE_1_TSKM", planAprCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿１月目
        fields.put("KKK_ZAIKO_RIEKI_1_TSKM", planAprChokuRiekiInven * 1000);
        // 計画＿直送利益＿１月目
        fields.put("KKK_CHKS_RIEKI_1_TSKM", planAprChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿２月目
        fields.put("KKK_ZAIKO_URG_2_TSKM", planMayAllSalInven * 1000);
        // 計画＿直送売上＿２月目
        fields.put("KKK_CHKS_URG_2_TSKM", planMayAllSalChokusou * 1000);
        // 計画＿在庫返品等＿２月目
        fields.put("KKK_ZAIKO_HMPNT_2_TSKM", planMayReturnInven * 1000);
        // 計画＿直送返品等＿２月目
        fields.put("KKK_CHKS_HMPNT_2_TSKM", planMayReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿２月目
        fields.put("KKK_ZAIKO_SHHR_RBT_2_TSKM", planMayRebateInven * 1000);
        // 計画＿直送支払リベート＿２月目
        fields.put("KKK_CHKS_SHHR_RBT_2_TSKM", planMayRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿２月目
        fields.put("KKK_ZAIKO_CNTR_FEE_2_TSKM", planMayCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿２月目
        fields.put("KKK_CHKS_CNTR_FEE_2_TSKM", planMayCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿２月目
        fields.put("KKK_ZAIKO_RIEKI_2_TSKM", planMayChokuRiekiInven * 1000);
        // 計画＿直送利益＿２月目
        fields.put("KKK_CHKS_RIEKI_2_TSKM", planMayChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿３月目
        fields.put("KKK_ZAIKO_URG_3_TSKM", planJunAllSalInven * 1000);
        // 計画＿直送売上＿３月目
        fields.put("KKK_CHKS_URG_3_TSKM", planJunAllSalChokusou * 1000);
        // 計画＿在庫返品等＿３月目
        fields.put("KKK_ZAIKO_HMPNT_3_TSKM", planJunReturnInven * 1000);
        // 計画＿直送返品等＿３月目
        fields.put("KKK_CHKS_HMPNT_3_TSKM", planJunReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿３月目
        fields.put("KKK_ZAIKO_SHHR_RBT_3_TSKM", planJunRebateInven * 1000);
        // 計画＿直送支払リベート＿３月目
        fields.put("KKK_CHKS_SHHR_RBT_3_TSKM", planJunRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿３月目
        fields.put("KKK_ZAIKO_CNTR_FEE_3_TSKM", planJunCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿３月目
        fields.put("KKK_CHKS_CNTR_FEE_3_TSKM", planJunCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿３月目
        fields.put("KKK_ZAIKO_RIEKI_3_TSKM", planJunChokuRiekiInven * 1000);
        // 計画＿直送利益＿３月目
        fields.put("KKK_CHKS_RIEKI_3_TSKM", planJunChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿４月目
        fields.put("KKK_ZAIKO_URG_4_TSKM", planJulAllSalInven * 1000);
        // 計画＿直送売上＿４月目
        fields.put("KKK_CHKS_URG_4_TSKM", planJulAllSalChokusou * 1000);
        // 計画＿在庫返品等＿４月目
        fields.put("KKK_ZAIKO_HMPNT_4_TSKM", planJulReturnInven * 1000);
        // 計画＿直送返品等＿４月目
        fields.put("KKK_CHKS_HMPNT_4_TSKM", planJulReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿４月目
        fields.put("KKK_ZAIKO_SHHR_RBT_4_TSKM", planJulRebateInven * 1000);
        // 計画＿直送支払リベート＿４月目
        fields.put("KKK_CHKS_SHHR_RBT_4_TSKM", planJulRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿４月目
        fields.put("KKK_ZAIKO_CNTR_FEE_4_TSKM", planJulCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿４月目
        fields.put("KKK_CHKS_CNTR_FEE_4_TSKM", planJulCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿４月目
        fields.put("KKK_ZAIKO_RIEKI_4_TSKM", planJulChokuRiekiInven * 1000);
        // 計画＿直送利益＿４月目
        fields.put("KKK_CHKS_RIEKI_4_TSKM", planJulChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿５月目
        fields.put("KKK_ZAIKO_URG_5_TSKM", planAugAllSalInven * 1000);
        // 計画＿直送売上＿５月目
        fields.put("KKK_CHKS_URG_5_TSKM", planAugAllSalChokusou * 1000);
        // 計画＿在庫返品等＿５月目
        fields.put("KKK_ZAIKO_HMPNT_5_TSKM", planAugReturnInven * 1000);
        // 計画＿直送返品等＿５月目
        fields.put("KKK_CHKS_HMPNT_5_TSKM", planAugReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿５月目
        fields.put("KKK_ZAIKO_SHHR_RBT_5_TSKM", planAugRebateInven * 1000);
        // 計画＿直送支払リベート＿５月目
        fields.put("KKK_CHKS_SHHR_RBT_5_TSKM", planAugRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿５月目
        fields.put("KKK_ZAIKO_CNTR_FEE_5_TSKM", planAugCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿５月目
        fields.put("KKK_CHKS_CNTR_FEE_5_TSKM", planAugCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿５月目
        fields.put("KKK_ZAIKO_RIEKI_5_TSKM", planAugChokuRiekiInven * 1000);
        // 計画＿直送利益＿５月目
        fields.put("KKK_CHKS_RIEKI_5_TSKM", planAugChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿６月目
        fields.put("KKK_ZAIKO_URG_6_TSKM", planSepAllSalInven * 1000);
        // 計画＿直送売上＿６月目
        fields.put("KKK_CHKS_URG_6_TSKM", planSepAllSalChokusou * 1000);
        // 計画＿在庫返品等＿６月目
        fields.put("KKK_ZAIKO_HMPNT_6_TSKM", planSepReturnInven * 1000);
        // 計画＿直送返品等＿６月目
        fields.put("KKK_CHKS_HMPNT_6_TSKM", planSepReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿６月目
        fields.put("KKK_ZAIKO_SHHR_RBT_6_TSKM", planSepRebateInven * 1000);
        // 計画＿直送支払リベート＿６月目
        fields.put("KKK_CHKS_SHHR_RBT_6_TSKM", planSepRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿６月目
        fields.put("KKK_ZAIKO_CNTR_FEE_6_TSKM", planSepCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿６月目
        fields.put("KKK_CHKS_CNTR_FEE_6_TSKM", planSepCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿６月目
        fields.put("KKK_ZAIKO_RIEKI_6_TSKM", planSepChokuRiekiInven * 1000);
        // 計画＿直送利益＿６月目
        fields.put("KKK_CHKS_RIEKI_6_TSKM", planSepChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿７月目
        fields.put("KKK_ZAIKO_URG_7_TSKM", planOctAllSalInven * 1000);
        // 計画＿直送売上＿７月目
        fields.put("KKK_CHKS_URG_7_TSKM", planOctAllSalChokusou * 1000);
        // 計画＿在庫返品等＿７月目
        fields.put("KKK_ZAIKO_HMPNT_7_TSKM", planOctReturnInven * 1000);
        // 計画＿直送返品等＿７月目
        fields.put("KKK_CHKS_HMPNT_7_TSKM", planOctReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿７月目
        fields.put("KKK_ZAIKO_SHHR_RBT_7_TSKM", planOctRebateInven * 1000);
        // 計画＿直送支払リベート＿７月目
        fields.put("KKK_CHKS_SHHR_RBT_7_TSKM", planOctRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿７月目
        fields.put("KKK_ZAIKO_CNTR_FEE_7_TSKM", planOctCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿７月目
        fields.put("KKK_CHKS_CNTR_FEE_7_TSKM", planOctCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿７月目
        fields.put("KKK_ZAIKO_RIEKI_7_TSKM", planOctChokuRiekiInven * 1000);
        // 計画＿直送利益＿７月目
        fields.put("KKK_CHKS_RIEKI_7_TSKM", planOctChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿８月目
        fields.put("KKK_ZAIKO_URG_8_TSKM", planNovAllSalInven * 1000);
        // 計画＿直送売上＿８月目
        fields.put("KKK_CHKS_URG_8_TSKM", planNovAllSalChokusou * 1000);
        // 計画＿在庫返品等＿８月目
        fields.put("KKK_ZAIKO_HMPNT_8_TSKM", planNovReturnInven * 1000);
        // 計画＿直送返品等＿８月目
        fields.put("KKK_CHKS_HMPNT_8_TSKM", planNovReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿８月目
        fields.put("KKK_ZAIKO_SHHR_RBT_8_TSKM", planNovRebateInven * 1000);
        // 計画＿直送支払リベート＿８月目
        fields.put("KKK_CHKS_SHHR_RBT_8_TSKM", planNovRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿８月目
        fields.put("KKK_ZAIKO_CNTR_FEE_8_TSKM", planNovCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿８月目
        fields.put("KKK_CHKS_CNTR_FEE_8_TSKM", planNovCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿８月目
        fields.put("KKK_ZAIKO_RIEKI_8_TSKM", planNovChokuRiekiInven * 1000);
        // 計画＿直送利益＿８月目
        fields.put("KKK_CHKS_RIEKI_8_TSKM", planNovChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿９月目
        fields.put("KKK_ZAIKO_URG_9_TSKM", planDecAllSalInven * 1000);
        // 計画＿直送売上＿９月目
        fields.put("KKK_CHKS_URG_9_TSKM", planDecAllSalChokusou * 1000);
        // 計画＿在庫返品等＿９月目
        fields.put("KKK_ZAIKO_HMPNT_9_TSKM", planDecReturnInven * 1000);
        // 計画＿直送返品等＿９月目
        fields.put("KKK_CHKS_HMPNT_9_TSKM", planDecReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿９月目
        fields.put("KKK_ZAIKO_SHHR_RBT_9_TSKM", planDecRebateInven * 1000);
        // 計画＿直送支払リベート＿９月目
        fields.put("KKK_CHKS_SHHR_RBT_9_TSKM", planDecRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿９月目
        fields.put("KKK_ZAIKO_CNTR_FEE_9_TSKM", planDecCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿９月目
        fields.put("KKK_CHKS_CNTR_FEE_9_TSKM", planDecCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿９月目
        fields.put("KKK_ZAIKO_RIEKI_9_TSKM", planDecChokuRiekiInven * 1000);
        // 計画＿直送利益＿９月目
        fields.put("KKK_CHKS_RIEKI_9_TSKM", planDecChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿１０月目
        fields.put("KKK_ZAIKO_URG_10_TSKM", planJanAllSalInven * 1000);
        // 計画＿直送売上＿１０月目
        fields.put("KKK_CHKS_URG_10_TSKM", planJanAllSalChokusou * 1000);
        // 計画＿在庫返品等＿１０月目
        fields.put("KKK_ZAIKO_HMPNT_10_TSKM", planJanReturnInven * 1000);
        // 計画＿直送返品等＿１０月目
        fields.put("KKK_CHKS_HMPNT_10_TSKM", planJanReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿１０月目
        fields.put("KKK_ZAIKO_SHHR_RBT_10_TSKM", planJanRebateInven * 1000);
        // 計画＿直送支払リベート＿１０月目
        fields.put("KKK_CHKS_SHHR_RBT_10_TSKM", planJanRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿１０月目
        fields.put("KKK_ZAIKO_CNTR_FEE_10_TSKM", planJanCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿１０月目
        fields.put("KKK_CHKS_CNTR_FEE_10_TSKM", planJanCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿１０月目
        fields.put("KKK_ZAIKO_RIEKI_10_TSKM", planJanChokuRiekiInven * 1000);
        // 計画＿直送利益＿１０月目
        fields.put("KKK_CHKS_RIEKI_10_TSKM", planJanChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿１１月目
        fields.put("KKK_ZAIKO_URG_11_TSKM", planFebAllSalInven * 1000);
        // 計画＿直送売上＿１１月目
        fields.put("KKK_CHKS_URG_11_TSKM", planFebAllSalChokusou * 1000);
        // 計画＿在庫返品等＿１１月目
        fields.put("KKK_ZAIKO_HMPNT_11_TSKM", planFebReturnInven * 1000);
        // 計画＿直送返品等＿１１月目
        fields.put("KKK_CHKS_HMPNT_11_TSKM", planFebReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿１１月目
        fields.put("KKK_ZAIKO_SHHR_RBT_11_TSKM", planFebRebateInven * 1000);
        // 計画＿直送支払リベート＿１１月目
        fields.put("KKK_CHKS_SHHR_RBT_11_TSKM", planFebRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿１１月目
        fields.put("KKK_ZAIKO_CNTR_FEE_11_TSKM", planFebCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿１１月目
        fields.put("KKK_CHKS_CNTR_FEE_11_TSKM", planFebCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿１１月目
        fields.put("KKK_ZAIKO_RIEKI_11_TSKM", planFebChokuRiekiInven * 1000);
        // 計画＿直送利益＿１１月目
        fields.put("KKK_CHKS_RIEKI_11_TSKM", planFebChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿１２月目
        fields.put("KKK_ZAIKO_URG_12_TSKM", planMarAllSalInven * 1000);
        // 計画＿直送売上＿１２月目
        fields.put("KKK_CHKS_URG_12_TSKM", planMarAllSalChokusou * 1000);
        // 計画＿在庫返品等＿１２月目
        fields.put("KKK_ZAIKO_HMPNT_12_TSKM", planMarReturnInven * 1000);
        // 計画＿直送返品等＿１２月目
        fields.put("KKK_CHKS_HMPNT_12_TSKM", planMarReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿１２月目
        fields.put("KKK_ZAIKO_SHHR_RBT_12_TSKM", planMarRebateInven * 1000);
        // 計画＿直送支払リベート＿１２月目
        fields.put("KKK_CHKS_SHHR_RBT_12_TSKM", planMarRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿１２月目
        fields.put("KKK_ZAIKO_CNTR_FEE_12_TSKM", planMarCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿１２月目
        fields.put("KKK_CHKS_CNTR_FEE_12_TSKM", planMarCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿１２月目
        fields.put("KKK_ZAIKO_RIEKI_12_TSKM", planMarChokuRiekiInven * 1000);
        // 計画＿直送利益＿１２月目
        fields.put("KKK_CHKS_RIEKI_12_TSKM", planMarChokuRiekiChokusou * 1000);
        // 実績見通し＿在庫売上＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_URG_10_TSKM", outlookJanAllSalInven * 1000);
        // 実績見通し＿直送売上＿１０月目
        fields.put("JSSK_MTSH_CHKS_URG_10_TSKM", outlookJanAllSalChokusou * 1000);
        // 実績見通し＿在庫返品等＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_HMPNT_10_TSKM", outlookJanReturnInven * 1000);
        // 実績見通し＿直送返品等＿１０月目
        fields.put("JSSK_MTSH_CHKS_HMPNT_10_TSKM", outlookJanReturnChokusou * 1000);
        // 実績見通し＿在庫支払リベート＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_SHHR_RBT_10_TSKM", outlookJanRebateInven * 1000);
        // 実績見通し＿直送支払リベート＿１０月目
        fields.put("JSSK_MTSH_CHKS_SHHR_RBT_10_TSKM", outlookJanRebateChokusou * 1000);
        // 実績見通し＿在庫センターフィ＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_CNTR_FEE_10_TSKM", outlookJanCenterFeeInven * 1000);
        // 実績見通し＿直送センターフィ＿１０月目
        fields.put("JSSK_MTSH_CHKS_CNTR_FEE_10_TSKM", outlookJanCenterFeeChokusou * 1000);
        // 実績見通し＿在庫利益＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_RIEKI_10_TSKM", outlookJanChokuRiekiInven * 1000);
        // 実績見通し＿直送利益＿１０月目
        fields.put("JSSK_MTSH_CHKS_RIEKI_10_TSKM", outlookJanChokuRiekiChokusou * 1000);
        // 実績見通し＿在庫売上＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_URG_11_TSKM", outlookFebAllSalInven * 1000);
        // 実績見通し＿直送売上＿１１月目
        fields.put("JSSK_MTSH_CHKS_URG_11_TSKM", outlookFebAllSalChokusou * 1000);
        // 実績見通し＿在庫返品等＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_HMPNT_11_TSKM", outlookFebReturnInven * 1000);
        // 実績見通し＿直送返品等＿１１月目
        fields.put("JSSK_MTSH_CHKS_HMPNT_11_TSKM", outlookFebReturnChokusou * 1000);
        // 実績見通し＿在庫支払リベート＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_SHHR_RBT_11_TSKM", outlookFebRebateInven * 1000);
        // 実績見通し＿直送支払リベート＿１１月目
        fields.put("JSSK_MTSH_CHKS_SHHR_RBT_11_TSKM", outlookFebRebateChokusou * 1000);
        // 実績見通し＿在庫センターフィ＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_CNTR_FEE_11_TSKM", outlookFebCenterFeeInven * 1000);
        // 実績見通し＿直送センターフィ＿１１月目
        fields.put("JSSK_MTSH_CHKS_CNTR_FEE_11_TSKM", outlookFebCenterFeeChokusou * 1000);
        // 実績見通し＿在庫利益＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_RIEKI_11_TSKM", outlookFebChokuRiekiInven * 1000);
        // 実績見通し＿直送利益＿１１月目
        fields.put("JSSK_MTSH_CHKS_RIEKI_11_TSKM", outlookFebChokuRiekiChokusou * 1000);
        // 実績見通し＿在庫売上＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_URG_12_TSKM", outlookMarAllSalInven * 1000);
        // 実績見通し＿直送売上＿１２月目
        fields.put("JSSK_MTSH_CHKS_URG_12_TSKM", outlookMarAllSalChokusou * 1000);
        // 実績見通し＿在庫返品等＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_HMPNT_12_TSKM", outlookMarReturnInven * 1000);
        // 実績見通し＿直送返品等＿１２月目
        fields.put("JSSK_MTSH_CHKS_HMPNT_12_TSKM", outlookMarReturnChokusou * 1000);
        // 実績見通し＿在庫支払リベート＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_SHHR_RBT_12_TSKM", outlookMarRebateInven * 1000);
        // 実績見通し＿直送支払リベート＿１２月目
        fields.put("JSSK_MTSH_CHKS_SHHR_RBT_12_TSKM", outlookMarRebateChokusou * 1000);
        // 実績見通し＿在庫センターフィ＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_CNTR_FEE_12_TSKM", outlookMarCenterFeeInven * 1000);
        // 実績見通し＿直送センターフィ＿１２月目
        fields.put("JSSK_MTSH_CHKS_CNTR_FEE_12_TSKM", outlookMarCenterFeeChokusou * 1000);
        // 実績見通し＿在庫利益＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_RIEKI_12_TSKM", outlookMarChokuRiekiInven * 1000);
        // 実績見通し＿直送利益＿１２月目
        fields.put("JSSK_MTSH_CHKS_RIEKI_12_TSKM", outlookMarChokuRiekiChokusou * 1000);

        // パラメータ.ファイル種別 = 3:見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜エリア＞　の場合
        // 計画＿総売上高計登録
        if(dataType.equals(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE))
        {
            // 計画＿総売上高計
            // 下記項目の合計値でセットする
            // CSV.【採算管理単位コード単位内の1行目】計画4月総売上高在庫～計画3月総売上高在庫
            // CSV.【採算管理単位コード単位内の2行目】計画4月総売上高直送～計画3月総売上高直送
            Long sumTotal = planAprAllSalInven + planMayAllSalInven + planJunAllSalInven + planJulAllSalInven +
                            planAugAllSalInven + planSepAllSalInven + planOctAllSalInven + planNovAllSalInven +
                            planDecAllSalInven + planJanAllSalInven + planFebAllSalInven + planMarAllSalInven +
                            planAprAllSalChokusou + planMayAllSalChokusou + planJunAllSalChokusou + planJulAllSalChokusou +
                            planAugAllSalChokusou + planSepAllSalChokusou + planOctAllSalChokusou + planNovAllSalChokusou +
                            planDecAllSalChokusou + planJanAllSalChokusou + planFebAllSalChokusou + planMarAllSalChokusou;

            fields.put("KKK_URG_KEI", sumTotal * 1000);
        }

        // プログラムIDをFunctionUtilから取得
        String programId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE,
                BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE).getFunctionId();
        if(dataType.equals(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE))
        {
            programId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE,
                    BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE).getFunctionId();
        }
        // 自動生成フィールド
        Date currentTime = new Date();
        String currentTimeStr = DateUtil.formatDateTime(currentTime);

        // 更新時は常に設定
        fields.put("KSHN_PRGRM_ID", programId);
        fields.put("RCRD_KSHN_NCHJ", currentTimeStr);

        return fields;
    }
}
